namespace SalesforceExpApi.Core.Exceptions
{
    /// <summary>
    /// Custom exceptions for specific error scenarios
    /// </summary>
    public class DataValidationException : Exception
    {
        public DataValidationException(string message) : base(message) { }
        public DataValidationException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class ExternalServiceException : Exception
    {
        public string ServiceName { get; }

        public ExternalServiceException(string serviceName, string message) : base(message)
        {
            ServiceName = serviceName;
        }

        public ExternalServiceException(string serviceName, string message, Exception innerException) : base(message, innerException)
        {
            ServiceName = serviceName;
        }
    }

    public class DataWeaveTransformationException : Exception
    {
        public DataWeaveTransformationException(string message) : base(message) { }
        public DataWeaveTransformationException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class RecordDependencyException : Exception
    {
        public RecordDependencyException(string message) : base(message) { }
        public RecordDependencyException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class SalesforceException : Exception
    {
        public string? ErrorCode { get; }

        public SalesforceException(string message) : base(message) { }
        public SalesforceException(string message, string errorCode) : base(message)
        {
            ErrorCode = errorCode;
        }
        public SalesforceException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class ConfigurationException : Exception
    {
        public ConfigurationException(string message) : base(message) { }
        public ConfigurationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
