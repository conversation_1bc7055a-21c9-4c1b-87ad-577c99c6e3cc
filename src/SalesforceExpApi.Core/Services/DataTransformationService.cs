using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using SalesforceExpApi.Core.Models.Common;
using SalesforceExpApi.Core.Services.Interfaces;

namespace SalesforceExpApi.Core.Services
{
    public class DataTransformationService : IDataTransformationService
    {
        private readonly ILogger<DataTransformationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly JsonSerializerOptions _jsonOptions;

        public DataTransformationService(ILogger<DataTransformationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public TransactionDetails BuildTransactionDetails(string correlationId, object salesforcePayload, RefIdResponse? refIdResponse, string operation, string objectType)
        {
            try
            {
                var priority = GetPriority(salesforcePayload);
                var enterpriseId = refIdResponse?.Response?.FirstOrDefault()?.EnterpriseId;

                return new TransactionDetails
                {
                    Transaction = new Transaction
                    {
                        CorrelationId = correlationId,
                        Operation = operation,
                        Source = "SALESFORCE",
                        Status = "QUEUED",
                        LastUpdatedBy = "EXPERIENCE_API",
                        EnterpriseId = enterpriseId,
                        Payload = SerializeToJson(salesforcePayload),
                        ObjectType = objectType,
                        Priority = priority,
                        RetryCount = 0,
                        QueryParams = null,
                        ErrorMsg = null,
                        ErrorType = null,
                        RecordId = GetRecordId(salesforcePayload)
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building transaction details for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        public T TransformPayload<T>(object source) where T : class, new()
        {
            try
            {
                if (source == null)
                    return new T();

                var json = SerializeToJson(source);
                return DeserializeFromJson<T>(json) ?? new T();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transforming payload to type {Type}", typeof(T).Name);
                throw;
            }
        }

        public string SerializeToJson(object obj)
        {
            try
            {
                return JsonSerializer.Serialize(obj, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serializing object to JSON");
                throw;
            }
        }

        public T? DeserializeFromJson<T>(string json) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return null;

                return JsonSerializer.Deserialize<T>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing JSON to type {Type}", typeof(T).Name);
                throw;
            }
        }

        public bool IsEmpty(object? obj)
        {
            return obj switch
            {
                null => true,
                string s => string.IsNullOrWhiteSpace(s),
                System.Collections.IEnumerable enumerable => !enumerable.Cast<object>().Any(),
                _ => false
            };
        }

        public string GetConfigValue(string key, string defaultValue = "")
        {
            return _configuration[key] ?? defaultValue;
        }

        private int GetPriority(object salesforcePayload)
        {
            try
            {
                // Try to extract priority from dynamic object
                if (salesforcePayload is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue("SyncPriority__c", out var priorityValue))
                    {
                        var priorityString = priorityValue?.ToString() ?? "0";
                        return int.TryParse(priorityString, out var result) ? result : 0;
                    }
                }

                // Try reflection for strongly typed objects
                var priorityProperty = salesforcePayload.GetType().GetProperty("SyncPriority__c");
                if (priorityProperty != null)
                {
                    var priorityValue = priorityProperty.GetValue(salesforcePayload)?.ToString() ?? "0";
                    return int.TryParse(priorityValue, out var result) ? result : 0;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string? GetRecordId(object salesforcePayload)
        {
            try
            {
                // Try to extract ID from dynamic object
                if (salesforcePayload is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue("Id", out var idValue))
                    {
                        return idValue?.ToString();
                    }
                }

                // Try reflection for strongly typed objects
                var idProperty = salesforcePayload.GetType().GetProperty("Id");
                if (idProperty != null)
                {
                    return idProperty.GetValue(salesforcePayload)?.ToString();
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
