using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Services.Interfaces
{
    public interface IDataTransformationService
    {
        TransactionDetails BuildTransactionDetails(string correlationId, object salesforcePayload, RefIdResponse? refIdResponse, string operation, string objectType);
        T TransformPayload<T>(object source) where T : class, new();
        string SerializeToJson(object obj);
        T? DeserializeFromJson<T>(string json) where T : class;
        bool IsEmpty(object? obj);
        string GetConfigValue(string key, string defaultValue = "");
    }
}
