using SalesforceExpApi.Core.Models.Requests;
using SalesforceExpApi.Core.Models.Responses;

namespace SalesforceExpApi.Core.Services.Interfaces
{
    public interface IOrderProcessingService
    {
        Task<CreateOrderResponse> CreateOrderAsync(CreateOrderRequest request, string correlationId, string transactionId, string businessKey);
        Task<UpdateOrderResponse> UpdateOrderAsync(UpdateOrderRequest request, string correlationId, string transactionId, string businessKey);
        Task<DeleteOrderResponse> DeleteOrderAsync(string orderNetsuiteId, string correlationId, string transactionId, string businessKey);
    }
}
