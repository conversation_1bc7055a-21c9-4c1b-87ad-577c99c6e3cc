using Microsoft.Extensions.DependencyInjection;
using SalesforceExpApi.Core.Services;
using SalesforceExpApi.Core.Services.Interfaces;

namespace SalesforceExpApi.Core.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddCoreServices(this IServiceCollection services)
        {
            // Register core services
            services.AddScoped<IOrderProcessingService, OrderProcessingService>();
            services.AddScoped<IInvoiceProcessingService, InvoiceProcessingService>();
            services.AddScoped<IAccountDeletionService, AccountDeletionService>();
            services.AddScoped<IDataTransformationService, DataTransformationService>();

            return services;
        }
    }
}
