namespace SalesforceExpApi.Core.Models.Common
{
    public class TransactionDetails
    {
        public Transaction Transaction { get; set; } = new();
    }

    public class Transaction
    {
        public string CorrelationId { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string LastUpdatedBy { get; set; } = string.Empty;
        public string? EnterpriseId { get; set; }
        public string Payload { get; set; } = string.Empty;
        public string? ObjectType { get; set; }
        public int Priority { get; set; }
        public int RetryCount { get; set; }
        public string? QueryParams { get; set; }
        public string? ErrorMsg { get; set; }
        public string? ErrorType { get; set; }
        public string? RecordId { get; set; }
    }

    public class RefId
    {
        public string ObjectType { get; set; } = string.Empty;
        public string? SalesforceId { get; set; }
        public string LastUpdatedBy { get; set; } = string.Empty;
        public string? EnterpriseId { get; set; }
        public string? LastPayloadSalesforce { get; set; }
    }

    public class RefIdRequest
    {
        public RefId RefId { get; set; } = new();
    }

    public class RefIdResponse
    {
        public List<RefIdResponseItem> Response { get; set; } = new();
    }

    public class RefIdResponseItem
    {
        public string? EnterpriseId { get; set; }
        public string? LastPayloadSalesforce { get; set; }
        public string? ObjectType { get; set; }
        public string? SalesforceId { get; set; }
        public string? LastUpdatedBy { get; set; }
    }
}
