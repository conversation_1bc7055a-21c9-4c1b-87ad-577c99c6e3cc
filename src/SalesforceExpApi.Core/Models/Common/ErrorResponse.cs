namespace SalesforceExpApi.Core.Models.Common
{
    /// <summary>
    /// Error response models matching MuleSoft error structure
    /// </summary>
    public class ErrorResponse
    {
        public int Code { get; set; }
        public string Status { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public ErrorDetails Response { get; set; } = new();
    }

    public class ErrorDetails
    {
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string? DetailedDescription { get; set; }
    }
}
