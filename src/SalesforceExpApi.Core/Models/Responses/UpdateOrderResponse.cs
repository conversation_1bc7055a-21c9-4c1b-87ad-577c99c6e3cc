using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Models.Responses
{
    public class UpdateOrderResponse : BaseResponse
    {
        public UpdateOrderResponseData Response { get; set; } = new();
    }

    public class UpdateOrderResponseData
    {
        public string Message { get; set; } = string.Empty;
        public string? OrderId { get; set; }
        public string? NetsuiteId { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}
