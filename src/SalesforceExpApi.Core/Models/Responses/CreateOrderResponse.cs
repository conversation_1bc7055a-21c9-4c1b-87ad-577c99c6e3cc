using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Models.Responses
{
    public class CreateOrderResponse : BaseResponse
    {
        public CreateOrderResponseData Response { get; set; } = new();
    }

    public class CreateOrderResponseData
    {
        public string Message { get; set; } = string.Empty;
        public string? OrderId { get; set; }
        public string? NetsuiteId { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}
