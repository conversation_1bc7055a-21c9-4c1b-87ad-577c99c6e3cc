using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Models.Responses
{
    public class UpdateInvoiceResponse : BaseResponse
    {
        public UpdateInvoiceResponseData Response { get; set; } = new();
    }

    public class UpdateInvoiceResponseData
    {
        public string Message { get; set; } = string.Empty;
        public string? InvoiceId { get; set; }
        public string? NetsuiteId { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}
