using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Models.Responses
{
    public class DeleteOrderResponse : BaseResponse
    {
        public DeleteOrderResponseData Response { get; set; } = new();
    }

    public class DeleteOrderResponseData
    {
        public string Message { get; set; } = string.Empty;
        public string? OrderId { get; set; }
        public string? NetsuiteId { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}
