using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Core.Models.Responses
{
    public class NotifyDeleteResponse : BaseResponse
    {
        public NotifyDeleteResponseData Response { get; set; } = new();
    }

    public class NotifyDeleteResponseData
    {
        public string Message { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? AccountId { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}
