using System.ComponentModel.DataAnnotations;

namespace SalesforceExpApi.Core.Models.Domain
{
    public class Order
    {
        [Required]
        public string SfId { get; set; } = string.Empty;
        
        public string? NetsuiteId { get; set; }
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        public string AccountId { get; set; } = string.Empty;
        
        public string? OpportunityId { get; set; }
        
        [Required]
        public string Status { get; set; } = string.Empty;
        
        [Required]
        public decimal TotalAmount { get; set; }
        
        public string? CurrencyIsoCode { get; set; }
        
        public DateTime? EffectiveDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        public string? BillingStreet { get; set; }
        
        public string? BillingCity { get; set; }
        
        public string? BillingState { get; set; }
        
        public string? BillingPostalCode { get; set; }
        
        public string? BillingCountry { get; set; }
        
        public string? ShippingStreet { get; set; }
        
        public string? ShippingCity { get; set; }
        
        public string? ShippingState { get; set; }
        
        public string? ShippingPostalCode { get; set; }
        
        public string? ShippingCountry { get; set; }
        
        public string? CustomerAuthorizedBy { get; set; }
        
        public DateTime? CustomerAuthorizedDate { get; set; }
        
        public string? CompanyAuthorizedBy { get; set; }
        
        public DateTime? CompanyAuthorizedDate { get; set; }
        
        public string? Type { get; set; }
        
        public string? OrderNumber { get; set; }
        
        public string? PoNumber { get; set; }
        
        public string? PoDate { get; set; }
        
        public bool IsReductionOrder { get; set; }
        
        public string? OriginalOrderId { get; set; }
        
        public string? DraftOrderId { get; set; }
        
        public string? ContractId { get; set; }
        
        public string? QuoteId { get; set; }
        
        public string? OwnerId { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public List<OrderItem> OrderItems { get; set; } = new();
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class OrderItem
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public string OrderId { get; set; } = string.Empty;
        
        [Required]
        public string Product2Id { get; set; } = string.Empty;
        
        public string? ProductCode { get; set; }
        
        public string? ProductName { get; set; }
        
        [Required]
        public decimal Quantity { get; set; }
        
        [Required]
        public decimal UnitPrice { get; set; }
        
        public decimal? ListPrice { get; set; }
        
        public decimal TotalPrice { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime? ServiceDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        public string? PricebookEntryId { get; set; }
        
        public string? OrderItemNumber { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }
}
