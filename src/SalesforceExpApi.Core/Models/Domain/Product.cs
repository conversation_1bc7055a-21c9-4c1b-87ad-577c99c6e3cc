using System.ComponentModel.DataAnnotations;

namespace SalesforceExpApi.Core.Models.Domain
{
    public class Product
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public string? ProductCode { get; set; }
        
        public string? Description { get; set; }
        
        public bool IsActive { get; set; }
        
        public string? Family { get; set; }
        
        public string? ExternalDataSourceId { get; set; }
        
        public string? ExternalId { get; set; }
        
        public string? DisplayUrl { get; set; }
        
        public string? QuantityUnitOfMeasure { get; set; }
        
        public bool IsDeleted { get; set; }
        
        public bool IsArchived { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public DateTime? SystemModstamp { get; set; }
        
        public string? StockKeepingUnit { get; set; }
        
        public string? SyncPriority__c { get; set; }
        
        public bool DoNotSync__c { get; set; }
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }
}
