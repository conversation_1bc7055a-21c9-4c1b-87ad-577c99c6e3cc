using System.ComponentModel.DataAnnotations;

namespace SalesforceExpApi.Core.Models.Domain
{
    public class Opportunity
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public string AccountId { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        public string StageName { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
        
        public decimal? Probability { get; set; }
        
        [Required]
        public DateTime CloseDate { get; set; }
        
        public string? Type { get; set; }
        
        public string? NextStep { get; set; }
        
        public string? LeadSource { get; set; }
        
        public bool IsClosed { get; set; }
        
        public bool IsWon { get; set; }
        
        public string? ForecastCategory { get; set; }
        
        public string? ForecastCategoryName { get; set; }
        
        public string? CampaignId { get; set; }
        
        public bool HasOpportunityLineItem { get; set; }
        
        public string? Pricebook2Id { get; set; }
        
        public string? OwnerId { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public DateTime? SystemModstamp { get; set; }
        
        public DateTime? LastActivityDate { get; set; }
        
        public string? FiscalQuarter { get; set; }
        
        public int? FiscalYear { get; set; }
        
        public string? Fiscal { get; set; }
        
        public string? ContactId { get; set; }
        
        public bool IsDeleted { get; set; }
        
        public string? Salesforce_OpportunityID__c { get; set; }
        
        public string? Artemis_ConfirmID__c { get; set; }
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }
}
