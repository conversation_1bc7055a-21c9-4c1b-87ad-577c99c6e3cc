using System.ComponentModel.DataAnnotations;

namespace SalesforceExpApi.Core.Models.Domain
{
    public class Invoice
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public string InvoiceNSId { get; set; } = string.Empty;
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        public string AccountId { get; set; } = string.Empty;
        
        public string? OpportunityId { get; set; }
        
        public string? OrderId { get; set; }
        
        [Required]
        public string Status { get; set; } = string.Empty;
        
        [Required]
        public decimal TotalAmount { get; set; }
        
        public decimal? TaxAmount { get; set; }
        
        public decimal? SubtotalAmount { get; set; }
        
        public decimal? DiscountAmount { get; set; }
        
        public string? CurrencyIsoCode { get; set; }
        
        public DateTime? InvoiceDate { get; set; }
        
        public DateTime? DueDate { get; set; }
        
        public DateTime? PaidDate { get; set; }
        
        public string? InvoiceNumber { get; set; }
        
        public string? PoNumber { get; set; }
        
        public string? BillingStreet { get; set; }
        
        public string? BillingCity { get; set; }
        
        public string? BillingState { get; set; }
        
        public string? BillingPostalCode { get; set; }
        
        public string? BillingCountry { get; set; }
        
        public string? ShippingStreet { get; set; }
        
        public string? ShippingCity { get; set; }
        
        public string? ShippingState { get; set; }
        
        public string? ShippingPostalCode { get; set; }
        
        public string? ShippingCountry { get; set; }
        
        public string? PaymentTerms { get; set; }
        
        public string? PaymentMethod { get; set; }
        
        public string? Notes { get; set; }
        
        public string? OwnerId { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public List<InvoiceLineItem> LineItems { get; set; } = new();
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class InvoiceLineItem
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public string InvoiceId { get; set; } = string.Empty;
        
        [Required]
        public string Product2Id { get; set; } = string.Empty;
        
        public string? ProductCode { get; set; }
        
        public string? ProductName { get; set; }
        
        [Required]
        public decimal Quantity { get; set; }
        
        [Required]
        public decimal UnitPrice { get; set; }
        
        public decimal? ListPrice { get; set; }
        
        public decimal TotalPrice { get; set; }
        
        public decimal? TaxAmount { get; set; }
        
        public decimal? DiscountAmount { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime? ServiceDate { get; set; }
        
        public DateTime? ServiceEndDate { get; set; }
        
        public string? LineItemNumber { get; set; }
        
        public string? TaxCode { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }
}
