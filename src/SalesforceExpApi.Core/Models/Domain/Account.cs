using System.ComponentModel.DataAnnotations;

namespace SalesforceExpApi.Core.Models.Domain
{
    public class Account
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        public string? AccountID__c { get; set; }
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public string? Type { get; set; }
        
        public string? Industry { get; set; }
        
        public string? Description { get; set; }
        
        public string? Website { get; set; }
        
        public string? Phone { get; set; }
        
        public string? Fax { get; set; }
        
        public int? NumberOfEmployees { get; set; }
        
        public decimal? AnnualRevenue { get; set; }
        
        public string? Rating { get; set; }
        
        public string? Ownership { get; set; }
        
        public string? TickerSymbol { get; set; }
        
        public string? BillingStreet { get; set; }
        
        public string? BillingCity { get; set; }
        
        public string? BillingState { get; set; }
        
        public string? BillingPostalCode { get; set; }
        
        public string? BillingCountry { get; set; }
        
        public string? BillingStreet1__c { get; set; }
        
        public string? BillingStreet2__c { get; set; }
        
        public string? ShippingStreet { get; set; }
        
        public string? ShippingCity { get; set; }
        
        public string? ShippingState { get; set; }
        
        public string? ShippingPostalCode { get; set; }
        
        public string? ShippingCountry { get; set; }
        
        public string? ParentId { get; set; }
        
        public string? OwnerId { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public string? CreatedById { get; set; }
        
        public DateTime LastModifiedDate { get; set; }
        
        public string? LastModifiedById { get; set; }
        
        public bool DoNotSync__c { get; set; }
        
        public string? AccountUpdatedBy__c { get; set; }
        
        public string? SyncPriority__c { get; set; }
        
        public bool IsDeleted { get; set; }
        
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }
}
