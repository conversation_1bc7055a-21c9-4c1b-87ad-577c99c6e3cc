namespace SalesforceExpApi.Infrastructure.Configuration
{
    public class HttpClientSettings
    {
        public TransactionDbApiConfig TransactionDbApi { get; set; } = new();
        public SyncPrcApiConfig SyncPrcApi { get; set; } = new();
        public OrderPrcApiConfig OrderPrcApi { get; set; } = new();
    }

    public class TransactionDbApiConfig
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; } = 443;
        public int ConnectionTimeout { get; set; } = 30000;
        public int ResponseTimeout { get; set; } = 30000;
        public int ReconnectionFrequency { get; set; } = 1000;
        public int ReconnectionAttempts { get; set; } = 3;
        public string TruststorePath { get; set; } = string.Empty;
        public string TruststorePassword { get; set; } = string.Empty;
        
        // API Endpoints
        public string RefIdPath { get; set; } = "/api/REF_ID";
        public string TransactionDetailsPath { get; set; } = "/api/TRANSACTION";
        public string TransactionTaskDetailsPath { get; set; } = "/api/TRANSACTION_TASK";
    }

    public class SyncPrcApiConfig
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; } = 443;
        public string BasePath { get; set; } = "/";
        public int ConnectionTimeout { get; set; } = 30000;
        public int ReconnectionFrequency { get; set; } = 1000;
        public int ReconnectionAttempts { get; set; } = 3;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string TruststorePath { get; set; } = string.Empty;
        public string TruststorePassword { get; set; } = string.Empty;
        
        // API Endpoints
        public string SyncRecordsPath { get; set; } = "/api/syncRecords";
        public string SyncProductsPath { get; set; } = "/api/syncProducts";
    }

    public class OrderPrcApiConfig
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; } = 443;
        public string BasePath { get; set; } = "/";
        public int ConnectionTimeout { get; set; } = 30000;
        public int ResponseTimeout { get; set; } = 185000;
        public int ReconnectionFrequency { get; set; } = 1000;
        public int ReconnectionAttempts { get; set; } = 3;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string TruststorePath { get; set; } = string.Empty;
        public string TruststorePassword { get; set; } = string.Empty;
        
        // API Endpoints
        public string OrdersPath { get; set; } = "/api/orders";
        public string InvoicesPath { get; set; } = "/api/invoices";
        public string OpportunitiesPath { get; set; } = "/api/opportunities";
    }

    public class SalesforceSettings
    {
        public string ConsumerKey { get; set; } = string.Empty;
        public string ConsumerSecret { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string SecurityToken { get; set; } = string.Empty;
        public string LoginUrl { get; set; } = "https://login.salesforce.com";
        public string ApiVersion { get; set; } = "v58.0";
        public SalesforceUserSettings User { get; set; } = new();
        public SalesforcePollSettings Poll { get; set; } = new();
        public int CreateTimeDiff { get; set; } = 300;
        public SalesforceChannelSettings Channel { get; set; } = new();
    }

    public class SalesforceUserSettings
    {
        public string MulesoftId { get; set; } = string.Empty;
    }

    public class SalesforcePollSettings
    {
        public int Frequency { get; set; } = 5000;
        public int StartDelay { get; set; } = 1000;
    }

    public class SalesforceChannelSettings
    {
        public SalesforceOpportunityChannelSettings OpportunityChangeEvent { get; set; } = new();
    }

    public class SalesforceOpportunityChannelSettings
    {
        public string Name { get; set; } = "/event/Ready_For_Fulfillment__e";
    }

    public class CertificateSettings
    {
        public string Path { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Type { get; set; } = "pfx";
    }
}
