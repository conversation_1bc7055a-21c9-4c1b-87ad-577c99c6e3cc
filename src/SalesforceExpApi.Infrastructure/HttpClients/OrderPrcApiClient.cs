using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;
using SalesforceExpApi.Infrastructure.Configuration;
using SalesforceExpApi.Core.Exceptions;

namespace SalesforceExpApi.Infrastructure.HttpClients
{
    public class OrderPrcApiClient : IOrderPrcApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<OrderPrcApiClient> _logger;
        private readonly HttpClientSettings _settings;
        private readonly JsonSerializerOptions _jsonOptions;

        public OrderPrcApiClient(
            HttpClient httpClient, 
            ILogger<OrderPrcApiClient> logger,
            IOptions<HttpClientSettings> settings)
        {
            _httpClient = httpClient;
            _logger = logger;
            _settings = settings.Value;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task<object> ProcessOrderAsync(object orderData, string method, string correlationId, string businessKey)
        {
            try
            {
                _logger.LogInformation("Processing order via {Method} for CorrelationId: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    method, correlationId, businessKey);

                var json = JsonSerializer.Serialize(orderData, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId, businessKey);

                HttpResponseMessage response = method.ToUpper() switch
                {
                    "POST" => await _httpClient.PostAsync(_settings.OrderPrcApi.OrdersPath, content),
                    "PUT" => await _httpClient.PutAsync(_settings.OrderPrcApi.OrdersPath, content),
                    "DELETE" => await _httpClient.DeleteAsync(_settings.OrderPrcApi.OrdersPath),
                    _ => throw new ArgumentException($"Unsupported HTTP method: {method}")
                };

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to process order. Method: {Method}, Status: {StatusCode}, Content: {Content}", 
                        method, response.StatusCode, errorContent);
                    throw new ExternalServiceException("OrderPrcApi", 
                        $"Failed to process order via {method}: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully processed order via {Method} for CorrelationId: {CorrelationId}", 
                    method, correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing order via {Method} for CorrelationId: {CorrelationId}", 
                    method, correlationId);
                throw;
            }
        }

        public async Task<object> ProcessInvoiceAsync(object invoiceData, string correlationId, string businessKey)
        {
            try
            {
                _logger.LogInformation("Processing invoice for CorrelationId: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    correlationId, businessKey);

                var json = JsonSerializer.Serialize(invoiceData, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId, businessKey);

                var response = await _httpClient.PutAsync(_settings.OrderPrcApi.InvoicesPath, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to process invoice. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("OrderPrcApi", 
                        $"Failed to process invoice: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully processed invoice for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing invoice for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        public async Task<object> ProcessOpportunityAsync(object opportunityData, string method, string correlationId, string businessKey)
        {
            try
            {
                _logger.LogInformation("Processing opportunity via {Method} for CorrelationId: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    method, correlationId, businessKey);

                var json = JsonSerializer.Serialize(opportunityData, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId, businessKey);

                HttpResponseMessage response = method.ToUpper() switch
                {
                    "POST" => await _httpClient.PostAsync(_settings.OrderPrcApi.OpportunitiesPath, content),
                    "PUT" => await _httpClient.PutAsync(_settings.OrderPrcApi.OpportunitiesPath, content),
                    _ => throw new ArgumentException($"Unsupported HTTP method for opportunities: {method}")
                };

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to process opportunity. Method: {Method}, Status: {StatusCode}, Content: {Content}", 
                        method, response.StatusCode, errorContent);
                    throw new ExternalServiceException("OrderPrcApi", 
                        $"Failed to process opportunity via {method}: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully processed opportunity via {Method} for CorrelationId: {CorrelationId}", 
                    method, correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing opportunity via {Method} for CorrelationId: {CorrelationId}", 
                    method, correlationId);
                throw;
            }
        }

        private void AddCorrelationHeaders(string correlationId, string businessKey)
        {
            _httpClient.DefaultRequestHeaders.Remove("correlationId");
            _httpClient.DefaultRequestHeaders.Remove("x-transactionId");
            _httpClient.DefaultRequestHeaders.Remove("x-businessKey");
            _httpClient.DefaultRequestHeaders.Remove("x-msg-timestamp");

            _httpClient.DefaultRequestHeaders.Add("correlationId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-transactionId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-businessKey", businessKey);
            _httpClient.DefaultRequestHeaders.Add("x-msg-timestamp", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
    }
}
