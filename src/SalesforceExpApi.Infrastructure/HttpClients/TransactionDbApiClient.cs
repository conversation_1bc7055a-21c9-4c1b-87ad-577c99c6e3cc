using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using SalesforceExpApi.Core.Models.Common;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;
using SalesforceExpApi.Infrastructure.Configuration;
using SalesforceExpApi.Core.Exceptions;

namespace SalesforceExpApi.Infrastructure.HttpClients
{
    public class TransactionDbApiClient : ITransactionDbApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TransactionDbApiClient> _logger;
        private readonly HttpClientSettings _settings;
        private readonly JsonSerializerOptions _jsonOptions;

        public TransactionDbApiClient(
            HttpClient httpClient, 
            ILogger<TransactionDbApiClient> logger,
            IOptions<HttpClientSettings> settings)
        {
            _httpClient = httpClient;
            _logger = logger;
            _settings = settings.Value;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task<RefIdResponse> InsertRefIdAsync(RefIdRequest request, string correlationId)
        {
            try
            {
                _logger.LogInformation("Inserting REF_ID record for CorrelationId: {CorrelationId}", correlationId);

                var json = JsonSerializer.Serialize(request, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId);

                var response = await _httpClient.PostAsync(_settings.TransactionDbApi.RefIdPath, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to insert REF_ID. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("TransactionDbApi", 
                        $"Failed to insert REF_ID: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<RefIdResponse>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully inserted REF_ID record for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new RefIdResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting REF_ID record for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        public async Task<RefIdResponse> GetRefIdAsync(string salesforceId, string objectType)
        {
            try
            {
                _logger.LogInformation("Getting REF_ID record for SalesforceId: {SalesforceId}, ObjectType: {ObjectType}", 
                    salesforceId, objectType);

                var queryParams = $"?SALESFORCE_ID={salesforceId}&OBJECT_TYPE={objectType}";
                var response = await _httpClient.GetAsync(_settings.TransactionDbApi.RefIdPath + queryParams);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to get REF_ID. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("TransactionDbApi", 
                        $"Failed to get REF_ID: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<RefIdResponse>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully retrieved REF_ID record for SalesforceId: {SalesforceId}", salesforceId);

                return result ?? new RefIdResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting REF_ID record for SalesforceId: {SalesforceId}", salesforceId);
                throw;
            }
        }

        public async Task<object> InsertTransactionAsync(TransactionDetails transactionDetails, string correlationId)
        {
            try
            {
                _logger.LogInformation("Inserting transaction details for CorrelationId: {CorrelationId}", correlationId);

                var json = JsonSerializer.Serialize(transactionDetails, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId);

                var response = await _httpClient.PostAsync(_settings.TransactionDbApi.TransactionDetailsPath, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to insert transaction details. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("TransactionDbApi", 
                        $"Failed to insert transaction details: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully inserted transaction details for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting transaction details for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        public async Task<object> InsertTransactionTaskAsync(object transactionTask, string correlationId)
        {
            try
            {
                _logger.LogInformation("Inserting transaction task for CorrelationId: {CorrelationId}", correlationId);

                var json = JsonSerializer.Serialize(transactionTask, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add correlation headers
                AddCorrelationHeaders(correlationId);

                var response = await _httpClient.PostAsync(_settings.TransactionDbApi.TransactionTaskDetailsPath, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to insert transaction task. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("TransactionDbApi", 
                        $"Failed to insert transaction task: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully inserted transaction task for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting transaction task for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        private void AddCorrelationHeaders(string correlationId)
        {
            _httpClient.DefaultRequestHeaders.Remove("correlationId");
            _httpClient.DefaultRequestHeaders.Remove("x-transactionId");
            _httpClient.DefaultRequestHeaders.Remove("x-msg-timestamp");

            _httpClient.DefaultRequestHeaders.Add("correlationId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-transactionId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-msg-timestamp", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
    }
}
