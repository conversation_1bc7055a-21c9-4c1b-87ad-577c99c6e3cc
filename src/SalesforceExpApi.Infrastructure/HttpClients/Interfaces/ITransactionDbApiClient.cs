using SalesforceExpApi.Core.Models.Common;

namespace SalesforceExpApi.Infrastructure.HttpClients.Interfaces
{
    public interface ITransactionDbApiClient
    {
        Task<RefIdResponse> InsertRefIdAsync(RefIdRequest request, string correlationId);
        Task<RefIdResponse> GetRefIdAsync(string salesforceId, string objectType);
        Task<object> InsertTransactionAsync(TransactionDetails transactionDetails, string correlationId);
        Task<object> InsertTransactionTaskAsync(object transactionTask, string correlationId);
    }
}
