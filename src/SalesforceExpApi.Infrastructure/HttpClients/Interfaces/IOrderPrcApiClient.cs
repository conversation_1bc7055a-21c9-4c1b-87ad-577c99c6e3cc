namespace SalesforceExpApi.Infrastructure.HttpClients.Interfaces
{
    public interface IOrderPrcApiClient
    {
        Task<object> ProcessOrderAsync(object orderData, string method, string correlationId, string businessKey);
        Task<object> ProcessInvoiceAsync(object invoiceData, string correlationId, string businessKey);
        Task<object> ProcessOpportunityAsync(object opportunityData, string method, string correlationId, string businessKey);
    }
}
