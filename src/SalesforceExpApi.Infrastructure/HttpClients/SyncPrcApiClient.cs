using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;
using SalesforceExpApi.Infrastructure.Configuration;
using SalesforceExpApi.Core.Exceptions;

namespace SalesforceExpApi.Infrastructure.HttpClients
{
    public class SyncPrcApiClient : ISyncPrcApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SyncPrcApiClient> _logger;
        private readonly HttpClientSettings _settings;
        private readonly JsonSerializerOptions _jsonOptions;

        public SyncPrcApiClient(
            HttpClient httpClient, 
            ILogger<SyncPrcApiClient> logger,
            IOptions<HttpClientSettings> settings)
        {
            _httpClient = httpClient;
            _logger = logger;
            _settings = settings.Value;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task<object> SyncRecordsAsync(string correlationId)
        {
            try
            {
                _logger.LogInformation("Calling sync records API for CorrelationId: {CorrelationId}", correlationId);

                // Add correlation headers
                AddCorrelationHeaders(correlationId);

                var response = await _httpClient.PostAsync(_settings.SyncPrcApi.SyncRecordsPath, null);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to sync records. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("SyncPrcApi", 
                        $"Failed to sync records: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully called sync records API for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling sync records API for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        public async Task<object> SyncProductsAsync(string correlationId)
        {
            try
            {
                _logger.LogInformation("Calling sync products API for CorrelationId: {CorrelationId}", correlationId);

                // Add correlation headers
                AddCorrelationHeaders(correlationId);

                var response = await _httpClient.PostAsync(_settings.SyncPrcApi.SyncProductsPath, null);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to sync products. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new ExternalServiceException("SyncPrcApi", 
                        $"Failed to sync products: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<object>(responseContent, _jsonOptions);

                _logger.LogInformation("Successfully called sync products API for CorrelationId: {CorrelationId}", correlationId);

                return result ?? new object();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling sync products API for CorrelationId: {CorrelationId}", correlationId);
                throw;
            }
        }

        private void AddCorrelationHeaders(string correlationId)
        {
            _httpClient.DefaultRequestHeaders.Remove("correlationId");
            _httpClient.DefaultRequestHeaders.Remove("x-transactionId");
            _httpClient.DefaultRequestHeaders.Remove("x-msg-timestamp");

            _httpClient.DefaultRequestHeaders.Add("correlationId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-transactionId", correlationId);
            _httpClient.DefaultRequestHeaders.Add("x-msg-timestamp", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
    }
}
