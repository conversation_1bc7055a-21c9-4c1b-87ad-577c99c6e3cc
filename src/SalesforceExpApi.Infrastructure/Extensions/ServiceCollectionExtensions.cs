using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using SalesforceExpApi.Infrastructure.Configuration;
using SalesforceExpApi.Infrastructure.HttpClients;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;

namespace SalesforceExpApi.Infrastructure.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure settings
            services.Configure<HttpClientSettings>(configuration.GetSection("HttpClients"));
            services.Configure<SalesforceSettings>(configuration.GetSection("Salesforce"));

            // Add HTTP clients
            services.AddHttpClientConfigurations(configuration);

            // Register HTTP client services
            services.AddScoped<ITransactionDbApiClient, TransactionDbApiClient>();
            services.AddScoped<ISyncPrcApiClient, SyncPrcApiClient>();
            services.AddScoped<IOrderPrcApiClient, OrderPrcApiClient>();

            return services;
        }

        private static IServiceCollection AddHttpClientConfigurations(this IServiceCollection services, IConfiguration configuration)
        {
            var httpConfig = configuration.GetSection("HttpClients").Get<HttpClientSettings>() ?? new HttpClientSettings();

            // Configure Transaction DB API HttpClient
            services.AddHttpClient<ITransactionDbApiClient, TransactionDbApiClient>("TransactionDbApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.TransactionDbApi.Host}:{httpConfig.TransactionDbApi.Port}");
                client.Timeout = TimeSpan.FromMilliseconds(httpConfig.TransactionDbApi.ResponseTimeout);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
                client.DefaultRequestHeaders.Add("x-source", "SALESFORCE");
                client.DefaultRequestHeaders.Add("sourceId", "SALESFORCE_EXP_API");
                client.DefaultRequestHeaders.Add("destinationId", "TRANSACTION_DB_SYS_API");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(
                httpConfig.TransactionDbApi.TruststorePath, 
                httpConfig.TransactionDbApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(
                httpConfig.TransactionDbApi.ReconnectionAttempts, 
                httpConfig.TransactionDbApi.ReconnectionFrequency));

            // Configure Sync Process API HttpClient
            services.AddHttpClient<ISyncPrcApiClient, SyncPrcApiClient>("SyncPrcApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.SyncPrcApi.Host}:{httpConfig.SyncPrcApi.Port}{httpConfig.SyncPrcApi.BasePath}");
                client.Timeout = TimeSpan.FromMilliseconds(30000);
                client.DefaultRequestHeaders.Add("client_id", httpConfig.SyncPrcApi.ClientId);
                client.DefaultRequestHeaders.Add("client_secret", httpConfig.SyncPrcApi.ClientSecret);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(
                httpConfig.SyncPrcApi.TruststorePath, 
                httpConfig.SyncPrcApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(
                httpConfig.SyncPrcApi.ReconnectionAttempts, 
                httpConfig.SyncPrcApi.ReconnectionFrequency));

            // Configure Order Process API HttpClient
            services.AddHttpClient<IOrderPrcApiClient, OrderPrcApiClient>("OrderPrcApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.OrderPrcApi.Host}:{httpConfig.OrderPrcApi.Port}{httpConfig.OrderPrcApi.BasePath}");
                client.Timeout = TimeSpan.FromMilliseconds(httpConfig.OrderPrcApi.ResponseTimeout);
                client.DefaultRequestHeaders.Add("client_id", httpConfig.OrderPrcApi.ClientId);
                client.DefaultRequestHeaders.Add("client_secret", httpConfig.OrderPrcApi.ClientSecret);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(
                httpConfig.OrderPrcApi.TruststorePath, 
                httpConfig.OrderPrcApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(
                httpConfig.OrderPrcApi.ReconnectionAttempts, 
                httpConfig.OrderPrcApi.ReconnectionFrequency));

            return services;
        }

        private static HttpMessageHandler CreateHttpMessageHandler(string truststorePath, string truststorePassword)
        {
            var handler = new HttpClientHandler();
            
            if (!string.IsNullOrEmpty(truststorePath))
            {
                try
                {
                    // Load certificate from truststore
                    var certificate = new X509Certificate2(truststorePath, truststorePassword);
                    handler.ClientCertificates.Add(certificate);
                    
                    // Configure SSL validation
                    handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
                    {
                        // Custom certificate validation logic
                        if (sslPolicyErrors == SslPolicyErrors.None)
                            return true;

                        // For development/testing, you might want to be more permissive
                        // In production, implement proper certificate validation
                        return sslPolicyErrors == SslPolicyErrors.RemoteCertificateNameMismatch ||
                               sslPolicyErrors == SslPolicyErrors.RemoteCertificateChainErrors;
                    };
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to load certificate from {truststorePath}: {ex.Message}");
                    // Continue without client certificate
                }
            }

            return handler;
        }

        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int maxRetries, int delayMs)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(
                    maxRetries,
                    retryAttempt => TimeSpan.FromMilliseconds(delayMs * retryAttempt),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        Console.WriteLine($"Retry {retryCount} after {timespan} seconds");
                    });
        }
    }
}
