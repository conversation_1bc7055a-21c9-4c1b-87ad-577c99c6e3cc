### HTTPS Listener ###
https.listener.keystore.keyPassword=![S/8oPgI8LOI=]
https.listener.keystore.password=![S/8oPgI8LOI=]
https.listener.truststore.password=![S/8oPgI8LOI=]

### HTTPS Request Mule API###
https.request.muleApi.truststore.password=![ykAUbYcewaMMPf/2TO23Xg==]

### Salesforce ###
salesforce.consumerKey=![lKUNswpoIVC7Zd3YytCtw1epYpJ52buhw/MiDgqgE5ual9iXK5KWTE3WprbJbqbIW6SnfOgVnTDsFmnEz4mf6WKnGNMslaSFdLD4Wg6PoESO1GluB7hICA==]
salesforce.keystorePassword=![YV4rV4nRGZXAAQzrj56X4w==]
salesforce.certificateAlias=![ykAUbYcewaPI6N5asQwgaqkn5eO+6P0MbQZTb3BlNDI=]

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.headers.clientId=![XMDcRZq//u6Ob/QLCCb/cIGt7ND6dLpEwaB+zv8u3KZZpPutYqJRqg==]
https.request.transactionDBSysApi.headers.clientSecret=![foXQyxLGFdwSL8Myonb9eIwrERZX3U6A5P1nBqWM3Ky2TpkI1YJ+sQ==]

### HTTPS Request syncPrc API ###
https.request.syncPrcApi.headers.clientId=![qnDRO+p3B2aXzoeUNumemiXAfuZDJgX6HutTx/qxt1Jp33J0NYPx/w==]
https.request.syncPrcApi.headers.clientSecret=![bKDFr+v+jG1UyDWRcJxXlWSjmE+ELEUNORRVZv92xBZEk8c26/rqwQ==]

### HTTPS Request orderPrc API ###
https.request.orderPrcApi.headers.clientId=![bys3ZDPCSoRcXa0DGrW+d4F94WuoSJ1SuxezDzEm03O5W2aakPxlDg==]
https.request.orderPrcApi.headers.clientSecret=![tLLVVh5V3aOcQEkGnx2ypHzw72EDayUed+xQ+Mry9Oz4s6c23w34QA==]