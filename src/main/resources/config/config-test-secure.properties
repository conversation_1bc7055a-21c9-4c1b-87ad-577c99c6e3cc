### HTTPS Listener ###
https.listener.keystore.keyPassword=![YKw7G+D4Lv0=]
https.listener.keystore.password=![YKw7G+D4Lv0=]
https.listener.truststore.password=![YKw7G+D4Lv0=]

### HTTPS Request Mule API ###
https.request.muleApi.truststore.password=![GYcHMiXzsEXRLmFlMQXgeQ==]

### Salesforce ###
salesforce.consumerKey=![MZOunaAFHde001hb8SWAFUXgbBuPAOknEgy8QsNIE9nJ2X4i8w3T+7AWUT3LJu0lFvnR12fg+eO3bPy4XIE3LV5Vz5rEsRIfAo47XM4dEkhFKC/eiSrcxg==]
salesforce.keystorePassword=![bM/0oaMMMjTGNHluY0YvwA==]
salesforce.certificateAlias=![GYcHMiXzsEUT4hwUN9nixVxHtl4It8uNFmWG0NIk2js=]

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.headers.clientId=![F6WgyW87Xxhe3bYuh+BA+9xNpVK5Bo4qbz/kicyNg/1GWB/7J4Y6qw==]
https.request.transactionDBSysApi.headers.clientSecret=![vgf8juMftk9mcLY+kbVO9FNaq/4mzEDkRnrbd+CE1IG2Dsdh/zyEZQ==]

### HTTPS Request syncPrc API ###
https.request.syncPrcApi.headers.clientId=![22GMWr4mpGYNCMRKQ52Fwodf1O+XpEO5lDG/FZxxTHkRr9u8oo9dpQ==]
https.request.syncPrcApi.headers.clientSecret=![Bue849vs0KpwhgbEiQiBSD7sEc4O4+NwV/IX2WICbTJJ1PxlN1x1EQ==]

### HTTPS Request orderPrc API ###
https.request.orderPrcApi.headers.clientId=![ZP6VPLHOuAr7FTzqKN4dugmTQoPBbyLnG7Kn8FU3VY4VkY6kox9n4w==]
https.request.orderPrcApi.headers.clientSecret=![xbKKx9o9eD1mxAbxkU8sX1zqJ+0bzzl0yfC4CpT59zucu4VdVkFTsg==]