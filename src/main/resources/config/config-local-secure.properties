### HTTPS Listener ###
https.listener.keystore.keyPassword=![AByDMj4STZQ=]
https.listener.keystore.password=![AByDMj4STZQ=]
https.listener.truststore.password=![AByDMj4STZQ=]

### HTTPS Request Mule API###
https.request.muleApi.truststore.password=![Y5AsVQgf5WqqWB3RV4nA4Q==]

### Salesforce ###
salesforce.consumerKey=![0gYAr7PNu0TGImLMhysZBAK/bk0nJIZWp8DNVGJOqFCruRzE0hJyZKeSKQgm1xG+z1A89GPtQNd4OFzjzWiq53j2KFnX+/hf+1FPl3Up2xHdF74OzyW4MA==]
salesforce.keystorePassword=![Y5AsVQgf5WqumnZmT4UtZj9XnxmYuB4W]
salesforce.certificateAlias=![NFyl0ooWHQRuguE5BOM6D5dYsV0za9Cx]

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.headers.clientId=![IwNbkemGEkJinvVWrAsUMvQC06GD+rs/7BP6EErFV1T56Nso49aVoQ==]
https.request.transactionDBSysApi.headers.clientSecret=![2G4GgxsJxpCuwW0Rb4oPvGVWYqtwr/eKnETLf/1D06cNovAYw+MLtA==]

### HTTPS Request syncPrc API ###
https.request.syncPrcApi.headers.clientId=![Aiu6KAtrZBbQke+kAP5dP5cUekwfuQ667nEf+f5nN60mNQxQmzz0Jw==]
https.request.syncPrcApi.headers.clientSecret=![z9x5oT1OsDyKeNmAESTxFe/bmRhEc9HT6QMh9CbXfjEeP8dW0rv9NA==]

### HTTPS Request orderPrc API ###
https.request.orderPrcApi.headers.clientId=![enBibRjArMGuBPeOk2z/BimQHk7BT1wOTEOdoRmgBWcmYhQNzR34hA==]
https.request.orderPrcApi.headers.clientSecret=![WHyQdGVu+H9roz2R+sKrypXikUkEbn6k7U5AgfWAZMsMFxyAeofadA==]
