### API ###
api.autodiscoveryId=18751744

### HTTPS Listener ###
https.listener.keystore.path=certificates/keystore-dev.jks
https.listener.truststore.path=certificates/truststore-dev.jks

### HTTPS Request Mule API###
https.request.muleApi.truststore.path=certificates/truststore-dev.jks

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.host=transactiondb-sys-api-dev-kby5ju.internal-1avcn3.usa-w2.cloudhub.io
https.request.transactionDBSysApi.port=443
https.request.transactionDBSysApi.connectionTimeout=30000
https.request.transactionDBSysApi.responseTimeout=30000
https.request.transactionDBSysApi.reconnection.frequency=1000
https.request.transactionDBSysApi.reconnection.attempts=3

### HTTPS Request syncPrc API ###
https.request.syncPrcApi.host=sync-prc-api-dev-kby5ju.internal-1avcn3.usa-w2.cloudhub.io
https.request.syncPrcApi.port=443
https.request.syncPrcApi.basePath=/
https.request.syncPrcApi.connectionTimeout=30000
https.request.syncPrcApi.responseTimeout=30000
https.request.syncPrcApi.reconnection.frequency=1000
https.request.syncPrcApi.reconnection.attempts=3

### HTTPS Request orderPrc API ###
https.request.orderPrcApi.host=order-prc-api-dev-kby5ju.internal-1avcn3.usa-w2.cloudhub.io
https.request.orderPrcApi.port=443
https.request.orderPrcApi.basePath=/
https.request.orderPrcApi.connectionTimeout=30000
https.request.orderPrcApi.responseTimeout=185000
https.request.orderPrcApi.reconnection.frequency=1000
https.request.orderPrcApi.reconnection.attempts=3

### Salesforce ###
salesforce.user.mulesoft.id=005Du000000JxSFIA0
salesforce.keystorePath=certificates/sf-keystore-dev.jks
### principal is user name ###
salesforce.principal=<EMAIL>
salesforce.tokenEndpoint=https://3degrees--devsandbox.sandbox.my.salesforce.com/services/oauth2/token
salesforce.audienceUrl=https://test.salesforce.com
salesforce.externalCallbackUrl=https://salesforce-sys-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io/callback
salesforce.authorizationUrl=https://3degrees--devsandbox.sandbox.my.salesforce.com/services/oauth2/authorize
salesforce.callbackPath=/callback
salesforce.authorizePath=/authorize
salesforce.poll.frequency=30000
salesforce.poll.startDelay=30000
salesforce.createTimeDiff=10
salesforce.connection.objectTtl=30
salesforce.connection.maxEntries=10
salesforce.reconnection.frequency=2000
salesforce.channel.opportunityChangeEvent.name=/event/Send_For_Fulfillment__e

### Poller flows state ###
accountCreatedFlowState=started
accountModifiedFlowState=started
productCreatedFlowState=started
productModifiedFlowState=started

### Enable sync for Salesforce ###
enableSyncForSalesforce=1
enableSyncForSalesforceCommodities=0
enableSyncForSalesforceServices=1
