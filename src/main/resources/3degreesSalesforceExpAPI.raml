#%RAML 1.0
title: Salesforce EXP API
description: Salesforce EXP API
version: v1
mediaType: application/json
#baseUri: https://localhost:8082/api/

uses:
  commonResources: /exchange_modules/a970b687-ceb1-48a0-9bc7-6fed0e331363/accelerator-common-resources-library/1.0.0/acceleratorCommonResourcesLibrary.raml
  common: /datatype/common.raml
  order: /datatype/order.raml
  invoice: /datatype/invoice.raml
securitySchemes:
  basic: !include /exchange_modules/a970b687-ceb1-48a0-9bc7-6fed0e331363/accelerator-common-resources-library/1.0.0/securitySchemes/basicAuthentication.raml
  clientIdEnforcement: !include /exchange_modules/a970b687-ceb1-48a0-9bc7-6fed0e331363/accelerator-common-resources-library/1.0.0/securitySchemes/clientIdEnforcement.raml
  
securedBy:
  - clientIdEnforcement

/notifyDelete:
  get:
    is: [commonResources.commonTraits,commonResources.trackable]
    securedBy:
      - clientIdEnforcement
    description: accepts the deletion notification for account record and deletes record if eligible for deletion.
    queryString:
      minProperties: 3
      maxProperties: 4
      properties:
        id:
          description: "Salesforce ID of the account"
          required: true
          type: string
        objectType:
          description: "type of object"
          required: true
          type: string   
        updatedBy:
          description: "type of object"
          required: true
          type: string  
        syncPriority:
          description: "Priority for processing this record"     
          required: false
          type: integer                   
    responses:
      200:
        body:
          application/json:
            type: common.notifyDeleteRes
            examples:
              output: !include /examples/common/notifyDeleteResponse.json

/orders:
  post:
    is: [commonResources.commonTraits,commonResources.trackable]
    securedBy:
      - clientIdEnforcement
    description: Request for creating Order (and related child objects) in Netsuite
    body:
      application/json:
        type: order.createOrderReq
        example: !include /examples/order/createOrderRequest.json
    responses:
      201:
        body:
          application/json:
            type: order.createOrderRes
            example: !include /examples/order/createOrderResponse.json

  put:
    is: [commonResources.commonTraits,commonResources.trackable]
    securedBy:
      - clientIdEnforcement
    description: Request for updating Order (and related child objects) in Netsuite
    body:
      application/json:
        type: order.updateOrderReq
        example: !include /examples/order/updateOrderRequest.json
    responses:
      200:
        body:
          application/json:
            type: order.updateOrderRes
            #example: !include /examples/order/upateOrderResponse.json
    
  delete:
    is: [commonResources.commonTraits,commonResources.trackable]
    securedBy:
      - clientIdEnforcement
    description: Request for deleting Order (and related child objects) in Netsuite
    queryParameters:
      orderNetsuiteId:
        type: string
        required: true
        displayName: "Netsuite Order Id"
        description: "Netsuite Order Id"
      billingScheduleNetsuiteIds:
        type: string
        required: true
        displayName: "Netsuite BillingSchedule Ids"
        description: "Netsuite BillingScheduleIds to be deleted separated by commas"
      billingScheduleDetailNetsuiteIds:
        type: string
        required: false
        displayName: "Netsuite BillingScheduleDetail Ids"
        description: "Netsuite BillingScheduleDetailIds to be deleted separated by commas"
      

    responses:
      200:
        body:
          application/json:
            type: order.deleteOrderRes
            example: !include /examples/order/deleteOrderResponse.json

/invoices:
  put:
    is: [commonResources.commonTraits,commonResources.trackable]
    securedBy:
      - clientIdEnforcement
    description: Request for updating invoice 
    body:
      application/json:
        type: invoice.updateInvoiceReq
        example: !include /examples/invoice/updateInvoiceRequest.json
    responses:
      200:
        body:
          application/json:
            type: invoice.updateInvoiceRes
            example: !include /examples/invoice/updateInvoiceResponse.json            