<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:os="http://www.mulesoft.org/schema/mule/os" xmlns:cloudhub="http://www.mulesoft.org/schema/mule/cloudhub"
    xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
    xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
    xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/cloudhub http://www.mulesoft.org/schema/mule/cloudhub/current/mule-cloudhub.xsd
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd">

    <error-handler name="global-error-handler">
        <!-- <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="ee6338b1-b3a2-43d3-be25-b88152281e4a" type="RECORD_DEPENDENCY:CAN_NOT_DELETE">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="c10127cf-af98-4e14-9dfb-52659f136c44" variableName="httpStatus" />
            <set-variable
                value='#[{&#10;&#10;	message: "CAN_NOT_DELETE",&#10;&#10;	details: error.description default ""&#10;&#10;}]'
                doc:name="errorDescription" doc:id="4f1b73a9-9647-4817-9c59-4ed93a980398" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="f99dc20d-6909-4053-9cc8-51a04b8d1618" name="common-error-sub-flow" />
        </on-error-propagate> -->
        
        <on-error-propagate type="APIKIT:BAD_REQUEST">
			<set-variable value='#[attributes.statusCode default "400"]' doc:name="httpStatus" doc:id="152d8c4d-f4f9-4c22-a041-48ce7cbee9ae" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;BAD_REQUEST&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="2702551e-2143-4207-9c96-919e80a07f41" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="9fda3f3d-1dbd-4483-b3e8-cd6735ffad76" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_FOUND">
			<set-variable value='#[attributes.statusCode default "404"]' doc:name="httpStatus" doc:id="60268dfd-a03c-46d0-9c93-0da9baced026" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_FOUND&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="5b178479-36e0-4f6d-9509-505f25b4600d" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="4b5f714a-e2cd-4c2c-b946-2f2f8be7e565" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:METHOD_NOT_ALLOWED">
			<set-variable value='#[attributes.statusCode default "405"]' doc:name="httpStatus" doc:id="a66ef79e-0c8e-456f-a9ef-838c5ec7cbf0" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;METHOD_NOT_ALLOWED&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="0a784bb3-a7aa-4df1-a54a-f0d9b66a4a30" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="ba5965b6-f33f-4dd0-a60d-12eb7a61e6d8" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_ACCEPTABLE">
			<set-variable value='#[attributes.statusCode default "406"]' doc:name="httpStatus" doc:id="4b9d4952-b388-4093-b028-b261818bb6cf" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_ACCEPTABLE&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="079fb219-9640-47ff-a860-5ff2914ceddf" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="9971e084-805d-4eab-94c8-eb3620dac986" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:UNSUPPORTED_MEDIA_TYPE">
			<set-variable value='#[attributes.statusCode default "415"]' doc:name="httpStatus" doc:id="c903694c-10f5-4857-ba5d-251658c4c820" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;UNSUPPORTED_MEDIA_TYPE&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="a59a45b0-446e-46fb-b340-e3d783b2c09f" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="8848c956-b928-441f-9482-d52acd74da33" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_IMPLEMENTED">
			<set-variable value='#[attributes.statusCode default "501"]' doc:name="httpStatus" doc:id="233e2d22-66c3-4082-b0ea-f6561b66185e" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_IMPLEMENTED&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="d540126c-ce64-4ec9-8cec-d8afe1a30b33" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="5e5cac70-4e31-44d0-8739-5b012753fc96" name="common-error-sub-flow" />
        </on-error-propagate>
        
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="464f3fdf-a89f-4263-b702-0f671c540945" type="EXPRESSION">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="5697132d-a345-4fbc-8053-aa0fbc06d6d9" variableName="httpStatus" />
            <set-variable
                value="#[output application/json&#10;---&#10;{&#10;	message: &quot;DATAWEAVE_EXPRESSION_ERROR&quot;,&#10;	details: error.description default &quot;&quot;,&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]"
                doc:name="errorDescription" doc:id="d17ac77b-f657-46e6-94ad-1fb4b0da3c4a" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="6ced93b1-7c9e-4908-8223-a187b78ddfb6" name="common-error-sub-flow" />
        
</on-error-propagate>
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="cb68e9f0-cd35-4b48-ac45-1d6af0b75965" type="ANY">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="fbcee8c0-7708-4082-b2d5-20b459f82306" variableName="httpStatus" />
            <set-variable
                value="#[output application/json&#10;---&#10;{&#10;	message: &quot;INTERNAL_SERVER_ERROR&quot;,&#10;	details: error.description default &quot;&quot;,&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]"
                doc:name="errorDescription" doc:id="fdd719a2-5e3a-4f5d-8a6f-ec4905d07d95" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="d01d30ff-394b-48d2-a102-87a529a2bbb4" name="common-error-sub-flow" />
        
</on-error-propagate>
    </error-handler>
    <sub-flow name="common-error-sub-flow" doc:id="34925282-c6b9-4bc2-8e16-84d6be099278">
		<set-payload
            value='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	code: vars.httpStatus,&#10;	status: "FAILURE",&#10;	transactionId: vars.vCorrelationId,&#10;	response: {&#10;		message: vars.errorDescription.message,&#10;		details: vars.errorDescription.details,&#10;		detailedDescription: vars.errorDescription.detailedDescription&#10;	}&#10;}]'
            doc:name="Final Error Response" doc:id="1351345f-f1e2-4f8b-9944-aeb44d50172a" />
        <logger level="ERROR" doc:name="Exit Generic Error Handler" doc:id="ce54e6bb-a9bd-42dc-b67c-691172c60e93"
            message='#[payload]' />
    
</sub-flow>
</mule>
