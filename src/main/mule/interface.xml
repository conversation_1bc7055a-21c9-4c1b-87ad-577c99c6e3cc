<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd ">
     <apikit:config outboundHeadersMapName="outboundHeadersMapName" httpStatusVarName="httpStatus" doc:name="Router" doc:id="355f3969-0add-41b4-8c5f-70c326284805" name="Router" disableValidations="true" />
	<flow name="3degreesSalesforceExpAPI-main">
        <http:listener path="/api/*" doc:name="/api/*" config-ref="HTTPS_Listener_config">
            <http:response statusCode="#[vars.httpStatus default 200]">
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[vars.httpStatus default 500]">
                <http:body><![CDATA[#[payload]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
        <ee:transform doc:name="Set vAttributes, vCorrelationId, vTransactionId,vMsgTimestamp" doc:id="738eeedc-fd39-4234-9402-f82402771293">
            <ee:variables>
                <ee:set-variable variableName="vCorrelationId"><![CDATA[output application/json --- attributes.headers.'correlationId' default correlationId]]></ee:set-variable>
                <ee:set-variable variableName="vAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
                <ee:set-variable variableName="vTransactionId"><![CDATA[output application/json --- attributes.headers.'x-transactionId' default correlationId]]></ee:set-variable>
                <ee:set-variable variableName="vMsgTimestamp"><![CDATA[%dw 2.0
output application/json
---
now()]]></ee:set-variable>
				<ee:set-variable variableName="vBusinessKey" ><![CDATA[%dw 2.0
output application/json
---
if((attributes.requestPath ~= "/api/orders") and ((attributes.method ~= "POST") or (attributes.method ~= "PUT")))
	payload.order.sfId
else if((attributes.requestPath ~= "/api/orders") and attributes.method ~= "DELETE")
	attributes.queryParams.orderNetsuiteId
else if(attributes.requestPath ~= "/api/invoices")
	payload.invoice.invoiceNSId
else null]]></ee:set-variable>
            </ee:variables>
        </ee:transform>
		<flow-ref doc:name="Flow Reference" doc:id="0b108f3f-0c37-4436-b08c-f3fa4f7918aa" name="pf-notify-sf-account-delete"/>
		<error-handler ref="global-error-handler" />
    </flow>
    <flow name="put:\invoices:application\json:3degreesSalesforceExpAPI-config">
        <flow-ref doc:name="pf-on-sf-invoice-update" doc:id="b8739293-f958-4bcc-be5c-278f02ba21a8" name="pf-on-sf-invoice-updated" />
    </flow>
    <flow name="put:\orders:application\json:3degreesSalesforceExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-sf-order-update" doc:id="d2f7f962-d78c-4aef-93d5-b6887ad18560" name="pf-on-sf-order-update" />
    </flow>
    <flow name="delete:\orders:3degreesSalesforceExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-sf-order-delete" doc:id="b102833f-48b3-4dd5-8585-723078a57426" name="pf-on-sf-order-delete" />
    </flow>
    <flow name="get:\notifyDelete:3degreesSalesforceExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-notify-sf-account-delete" doc:id="a420811b-44fc-405f-b429-cd9c7126a25a" name="pf-notify-sf-account-delete" />
        <error-handler ref="global-error-handler" />
    </flow>
    <flow name="post:\orders:application\json:3degreesSalesforceExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-sf-order-create" doc:id="2f5f4224-551e-4ebb-986b-773987c08b29" name="pf-on-sf-order-create" />
    </flow>
</mule>
