<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce" xmlns:salesforce-pub-sub="http://www.mulesoft.org/schema/mule/salesforce-pub-sub"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd 
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/salesforce-pub-sub http://www.mulesoft.org/schema/mule/salesforce-pub-sub/current/mule-salesforce-pub-sub.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd">
	<flow name="pf-on-sf-opportunity-modified" doc:id="6d313e11-d87c-4517-83ca-ed91465e4ba1" >
		<salesforce:subscribe-channel-listener streamingChannel="${salesforce.channel.opportunityChangeEvent.name}" doc:name="/event/Ready_For_Fulfillment__e" doc:id="5bf1daa5-c46c-48dc-ae22-a364119b8bed" config-ref="Salesforce_Config"/>
		<ee:transform
			doc:name="Set vBusinessKey,  vCorrelationId, vMsgTimestamp, vTransactionId"
			doc:id="4f550f28-e7e4-498b-9d0f-19f4f2b63f96">
			<ee:variables>
				<ee:set-variable variableName="vCorrelationId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vTransactionId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vBusinessKey" ><![CDATA[%dw 2.0
output application/json
---
"OpportunitySalesforceId-" ++ (payload['data']['payload'].Salesforce_OpportunityID__c default "")]]></ee:set-variable>
				<ee:set-variable variableName="vMsgTimestamp" ><![CDATA[%dw 2.0
output application/json
---
now()]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="edde4db3-51b6-474d-8a57-50b0709598a1"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-opportunity-modified", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="db640e2c-df99-425e-9484-8a8b7a577572"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-sf-opportunity-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="c8be1f57-c7ae-443a-ac51-85e65a6c4f28"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-sf-opportunity-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<set-variable value="#[output application/json&#10;---&#10;if(isEmpty(payload['data']['payload'].Artemis_ConfirmID__c)) &quot;POST&quot;&#10;else &quot;PUT&quot;]" doc:name="vRequestHTTPMethod" doc:id="daeabc6b-3755-4b82-b276-6c658644ab0e" variableName="vRequestHTTPMethod"/>
		<http:request method="#[vars.vRequestHTTPMethod]" doc:name="Call OrderPrc to process opportunity" doc:id="2c8401b9-ea09-4113-be17-b712b7e8a6ed" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.opportunities.path')]">
			<http:body ><![CDATA[#[output application/json
---
{
	"opportunity": {
		"sfId": payload['data']['payload'].Salesforce_OpportunityID__c
	}
}]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "salesforce-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vTransactionId,
	"sourceId" : "salesforce-exp-api",
	"x-businessKey" : vars.vBusinessKey
}]]]></http:headers>
		</http:request>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="1beb7121-539d-4262-a30a-4e8e777bbdbd"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-sf-opportunity-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="05bae8d1-0795-4f01-a01d-74eef036c554"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-sf-opportunity-modified",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="bbc9ce43-44d5-4678-b81d-b0b3143e744b"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-opportunity-modified",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<error-handler ref="global-error-handler" />
	</flow>

</mule>