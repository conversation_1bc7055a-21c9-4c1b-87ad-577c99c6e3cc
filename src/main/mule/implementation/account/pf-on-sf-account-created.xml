<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-sf-account-created"
		doc:id="0ba94cf0-258d-4314-bc20-b7e28d66ab50" maxConcurrency="1" initialState="${accountCreatedFlowState}">
		<salesforce:new-object-listener
			doc:name="On Account record created"
			doc:id="75be42ac-4814-40b8-858f-ab0d8e9f18c4"
			config-ref="Salesforce_Config" objectType="Account">
			<scheduling-strategy>
				<fixed-frequency frequency="${salesforce.poll.frequency}" startDelay="${salesforce.poll.startDelay}"/>
			</scheduling-strategy>
		</salesforce:new-object-listener>
		<ee:transform
			doc:name="Set vSalesforcePayload, vCorrelationId, vTransactionId"
			doc:id="489e46c7-a726-4a9c-b058-654246e9dd80">
			<ee:variables>
				<ee:set-variable variableName="vCorrelationId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vTransactionId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vSalesforcePayload" ><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="b65074dc-7687-4d09-abe3-6f5e93016777"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-account-created", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="ee5b73e9-af1d-4de8-b66d-0498735f3c73"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-sf-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="5ca6ea4a-6dec-4bd4-aaf0-3754c52cbb07"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-sf-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendRequest": payload&#10;}]' />

		<choice doc:name="Check enableSyncForSalesforce" doc:id="2c74b852-2490-4548-a807-1fb827bc073b" >
			<when expression="#[Mule::p('enableSyncForSalesforce') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is enabled" doc:id="4c4b2759-1f67-4a60-9b9c-914ef87b6b81" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForSalesforce is enabled", &#10;	"FlowName" : "pf-on-sf-account-created", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<choice doc:name="Check if sync is required" doc:id="aa544a80-16ff-4e6d-b96b-d63604436c50">
			<when expression="#[((vars.vSalesforcePayload.'LastModifiedById' != Mule::p('salesforce.user.mulesoft.id')) and (vars.vSalesforcePayload.'DoNotSync__c' == &quot;false&quot;))]">
				<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;headers&quot;: {&#10;		&quot;x-source&quot;: &quot;SALESFORCE&quot;,&#10;		&quot;x-transactionId&quot;: vars.vTransactionId,&#10;		&quot;x-msg-timestamp&quot;: (now() as LocalDateTime {format: &quot;yyyy-MM-dd'T'HH:mm:ss.000'Z'&quot;}),&#10;		&quot;correlationId&quot;: vars.vCorrelationId,&#10;		&quot;sourceId&quot;: &quot;SALESFORCE_EXP_API&quot;,&#10;		&quot;destinationId&quot;: &quot;TRANSACTION_DB_SYS_API&quot;,&#10;		&quot;content-type&quot;: &quot;application/json&quot;&#10;	}&#10;}]" doc:name="vRequestAttributes" doc:id="6b7d1af8-5fd3-42ba-b5c6-27289858bd6b" variableName="vRequestAttributes" />
				<http:request method="POST" doc:name="Insert into REF_ID" doc:id="5a7884db-4cde-4787-a059-bb8e04472646" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/REF_ID" target="vInsertRefIdResponse">
					<http:body><![CDATA[#[%dw 2.0
output application/json skipNullOn='everywhere'
---
{
   "refID":{
      "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type'),
      "SALESFORCE_ID": vars.vSalesforcePayload.'AccountID__c',
      "LAST_UPDATED_BY": "EXPERIENCE_API"
  }
}]]]></http:body>
					<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
				</http:request>
				<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="3c4bfaa3-ff96-442a-abf4-6eb8d338f75c">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
output application/json
---
{
    "transaction": {
		"CORRELATION_ID": vars.vCorrelationId,
	    "OPERATION": "CREATE",
	    "SOURCE": "SALESFORCE",
	    "STATUS": "QUEUED",
	    "LAST_UPDATED_BY": "EXPERIENCE_API",
	    "ENTERPRISE_ID":  vars.vInsertRefIdResponse.response.'ENTERPRISE_ID', 
	    "PAYLOAD": write(vars.vSalesforcePayload,'application/json'),
	    "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type') default null,
	    "PRIORITY": (vars.vSalesforcePayload.'SyncPriority__c' default "0") as Number as String {format:"0"} as Number,
	    "RETRY_COUNT": 0,
	    "QUERY_PARAMS": null,
	    "ERROR_MSG": null,
	    "ERROR_TYPE": null
	}
}]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAILS" doc:id="9d088278-1fd5-4d86-9fab-4c8017d9b115" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/TRANSACTION" target="vInsertTransactionResponse">
					<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
					<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
				</http:request>
						<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="c79967c7-6346-4404-9217-8a9a29e745ab" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')]" target="vSyncRecordResponse">
								<http:body ><![CDATA[#[{}]]]></http:body>
							<http:headers><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
							</http:request>
						<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="a8a74c2d-83f6-4a5d-8b8a-8a3bc1a6c6e3" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-on-sf-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]'/>
						<ee:transform doc:name="Set payload, httpStatus" doc:id="4d357046-19a4-43c6-bd6f-491eb41844c3" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 201,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
  	"eid": vars.vInsertRefIdResponse.response.'ENTERPRISE_ID',
  	"id": vars.vSalesforcePayload.'AccountID__c'
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[201]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: Discard sync" doc:id="6f82314b-ca8a-49a1-9b33-d01f536c8a55" message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: (&#10;		if(vars.vSalesforcePayload.'LastModifiedById' == Mule::p('salesforce.user.mulesoft.id'))&#10;			&quot;Account has been created by Mule flow. NO Sync is required for this account&quot;&#10;		else&#10;			&quot;Account has been set to DO_NOT_SYNC. NO Sync is required for this account&quot;&#10;	),&#10;	&quot;FlowName&quot;: &quot;pf-on-sf-account-created&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId&#10;}]" />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="935f12a3-e26c-44cf-b20f-b77518f7a9ac" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": (
		if(vars.vSalesforcePayload.'LastModifiedById' == Mule::p('salesforce.user.mulesoft.id'))
			"Account has been created by Mule flow"
		else
			"Account has been set to DO_NOT_SYNC"
	)
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</otherwise>
		</choice>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is disabled" doc:id="04cddb05-5a9c-426f-ab63-167db290ee47" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForSalesforce is disabled", &#10;	"FlowName" : "pf-on-sf-account-created", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
				<ee:transform doc:name="Set payload, httpStatus" doc:id="82a3024f-4ab6-422d-b8d7-9f1b05dea8b6" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForSalesforce is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="d0ff4f6c-6daf-4966-be43-76d2e071dc18"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-sf-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="eefec5de-43ce-484d-b71c-c26848d9ae65"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-sf-account-created",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="eb3517dd-d69e-4d24-a025-e2aed0880ef1"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-account-created",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
		<error-handler ref="global-error-handler" />
	</flow>

</mule>