<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-sf-account-modified"
		doc:id="14cbf152-b6ae-425f-a6f5-1d4f9dc201fb" maxConcurrency="1" initialState="${accountModifiedFlowState}">
		<salesforce:modified-object-listener
			doc:name="On Account record modified"
			doc:id="1327b729-9166-4fec-bda0-63ef686ff580"
			config-ref="Salesforce_Config" objectType="Account">
			<scheduling-strategy>
				<fixed-frequency frequency="${salesforce.poll.frequency}" startDelay="${salesforce.poll.startDelay}"/>
			</scheduling-strategy>
		</salesforce:modified-object-listener>
		<logger level="INFO" doc:name="LOG INFO: Timestamps" doc:id="ffff5cd6-833f-47d4-ae53-d99002f1a6e9" message="#[output application/json&#10;---&#10;{&#10;	&quot;Id&quot;: payload.'Id',&#10;	&quot;Name&quot;: payload.'Name',&#10;	&quot;LastModifiedDate&quot;: payload.'LastModifiedDate',&#10;	&quot;CreatedDate&quot;: payload.'CreatedDate'&#10;}]"/>
		<choice doc:name="Check if record is created"
			doc:id="117e0487-9d9c-4776-b856-f732af168cb2">
			<when
				expression="#[%dw 2.0&#10;var lastModifiedDate = payload.'LastModifiedDate' default now() as DateTime&#10;var createdDate = payload.'CreatedDate' default now() as DateTime&#10;output application/json&#10;&#10;---&#10;(abs((lastModifiedDate - createdDate).seconds) &gt; Mule::p('salesforce.createTimeDiff'))]">
				<flow-ref
					doc:name="Flow Reference to sf-create-record-in-db"
					doc:id="1805263c-8939-4dab-98a1-8ea703e37a3e"
					name="sf-create-record-in-db" />
			</when>
		</choice>
		<error-handler ref="global-error-handler" />
	</flow>
	<sub-flow name="sf-create-record-in-db"
		doc:id="3d26fe9a-8be0-4dc5-9acc-ab47726fef0e">
		<ee:transform
			doc:name="Set vAttributes, vCorrelationId, vSalesforcePayload, vTransactionId"
			doc:id="2d19c8f1-4994-4267-8923-5888a31963d3">
			<ee:variables>
				<ee:set-variable variableName="vCorrelationId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vTransactionId"><![CDATA[output application/json --- correlationId]]></ee:set-variable>
				<ee:set-variable variableName="vSalesforcePayload" ><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-variable>
				<ee:set-variable variableName="vAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="d902bfab-e0c9-43ae-bf1a-2ebbcfcc233e"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-account-modified", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="a9275e18-7cf3-4ab7-b7a3-0710d63732ff"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-sf-account-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="a7efd227-3a4e-456b-9b25-3be78172f14a"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-sf-account-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendRequest": payload&#10;}]' />
		<choice doc:name="Check enableSyncForSalesforce" doc:id="5b06a011-bad4-4f6d-be83-02251037af54" >
			<when expression="#[Mule::p('enableSyncForSalesforce') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is enabled" doc:id="72db8c99-4d33-432a-9062-7c16ea7d271e" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForSalesforce is enabled", &#10;	"FlowName" : "pf-on-sf-account-modified", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
				<choice doc:name="Check LastModifiedById" doc:id="8330c793-bf14-475e-aecf-9848619e6911">
			<when expression="#[(payload.'LastModifiedById' != Mule::p('salesforce.user.mulesoft.id'))]">
				<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;headers&quot;: {&#10;		&quot;x-source&quot;: &quot;SALESFORCE&quot;,&#10;		&quot;x-transactionId&quot;: vars.vTransactionId,&#10;		&quot;x-msg-timestamp&quot;: (now() as LocalDateTime {format: &quot;yyyy-MM-dd'T'HH:mm:ss.000'Z'&quot;}),&#10;		&quot;correlationId&quot;: vars.vCorrelationId,&#10;		&quot;sourceId&quot;: &quot;SALESFORCE_EXP_API&quot;,&#10;		&quot;destinationId&quot;: &quot;TRANSACTION_DB_SYS_API&quot;,&#10;		&quot;content-type&quot;: &quot;application/json&quot;&#10;	}&#10;}]" doc:name="vRequestAttributes" doc:id="2b9d4c60-288f-4522-b9a6-68121f21375c" variableName="vRequestAttributes" />
				<http:request method="GET" doc:name="Fetch ENTERPRISE_ID from REF_ID" doc:id="fc691176-6214-4c2a-b4f3-8cb18fe9d413" target="vRefIdResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/REF_ID">
					<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
					<http:query-params><![CDATA[#[output application/java
---
{
	"SALESFORCE_ID": vars.vSalesforcePayload.'Id',
	"OBJECT_TYPE": "ACCOUNT"
}]]]></http:query-params>
				</http:request>
				<choice doc:name="Check if record exists in REF_ID" doc:id="025bb642-66ad-4be2-bdaa-ac70b8688578">
							<when expression="#[!(isEmpty(vars.vRefIdResponse.response))]">
								<ee:transform doc:name="vSyncableFieldUpdateFlag" doc:id="1d5560bf-918f-4a4b-add0-5becf92c6848">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="vSyncableFieldUpdateFlag"><![CDATA[%dw 2.0
var vRefIdRecord = if(isEmpty(vars.vRefIdResponse.response[0].'LAST_PAYLOAD_SALESFORCE')) null else (read(vars.vRefIdResponse.response[0].'LAST_PAYLOAD_SALESFORCE' default "{}", "application/json")).'account'
output application/json
---
if(vRefIdRecord ~= null)
	true
else if(
	(vRefIdRecord.name ~= vars.vSalesforcePayload.Name) and
	// (vRefIdRecord.accountUpdatedBy__c ~= vars.vSalesforcePayload.AccountUpdatedBy__c) and
	(vRefIdRecord.billingStreet1__c ~= vars.vSalesforcePayload.BillingStreet1__c) and
	(vRefIdRecord.billingStreet2__c ~= vars.vSalesforcePayload.BillingStreet2__c) and
	(vRefIdRecord.billingCity ~= vars.vSalesforcePayload.BillingCity) and
	(vRefIdRecord.billingState ~= vars.vSalesforcePayload.BillingState) and
	(vRefIdRecord.billingPostalCode ~= vars.vSalesforcePayload.BillingPostalCode) and
	(vRefIdRecord.billingCountry ~= vars.vSalesforcePayload.BillingCountry) and
	(vRefIdRecord.eid ~= vars.vSalesforcePayload.EnterpriseID__c) 
) false
else 
	true]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
								<choice doc:name="Check if sync is required" doc:id="4e7e6673-bc32-44ca-b7ea-b09df69b85bc">
					<when expression="#[((vars.vSyncableFieldUpdateFlag) and ((vars.vSalesforcePayload.'DoNotSync__c' == &quot;false&quot;) and ((isEmpty(vars.vRefIdResponse.'response')) or !(vars.vRefIdResponse.response[0].'DO_NOT_SYNC'))))]">
						<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="30269f10-ea60-4475-8545-9cc21f5e7747">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "UPDATE",
    "SOURCE": "SALESFORCE",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID":  vars.vRefIdResponse.response[0].'ENTERPRISE_ID', 
    "PAYLOAD": write(vars.vSalesforcePayload,'application/json'),
    "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type') default null,
    "RETRY_COUNT": 0,
    "PRIORITY": (vars.vSalesforcePayload.'SyncPriority__c' default "0") as Number as String {format:"0"} as Number,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
		"val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null
  }
}]]></ee:set-variable>
			</ee:variables>
		
</ee:transform>
						<http:request method="POST" doc:name="Insert into TRANSACTION_DETAIL" doc:id="b4f72965-1044-48d2-8768-ad73161fa664" target="vInsertTransactionResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/TRANSACTION">
							<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
							<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
						</http:request>
										<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="4725648c-fab4-455e-b134-0f828e09dd35" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')]" target="vSyncRecordResponse">
												<http:body ><![CDATA[#[{}]]]></http:body>
											<http:headers><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
											</http:request>
										<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="6985ad62-f2f9-4ee7-9820-361ab17dd7c6" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-on-sf-account-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
										<ee:transform doc:name="Set payload, httpStatus" doc:id="c33de53f-4a19-4245-8a01-dc21e0f3b7ea" >
											<ee:message >
												<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
  	"eid": vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default "",
  	"id": vars.vSalesforcePayload.'Id'  default "",
  }
}]]></ee:set-payload>
											</ee:message>
											<ee:variables >
												<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
											</ee:variables>
										</ee:transform>
					</when>
					<when expression="#[((vars.vSyncableFieldUpdateFlag) and !((vars.vSalesforcePayload.'DoNotSync__c' == &quot;false&quot;) and ((isEmpty(vars.vRefIdResponse.'response')) or !(vars.vRefIdResponse.response[0].'DO_NOT_SYNC'))))]">
										<logger level="INFO" doc:name="LOG INFO: Discard sync" doc:id="2fa6c528-a331-4a40-8b9d-ed4ccd3bc0d8" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Account has been set to DO_NOT_SYNC. NO Sync is required for this contact",&#10;	"FlowName": "pf-on-sf-account-modified",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
										<ee:transform doc:name="Set payload, httpStatus" doc:id="fed574e4-fd03-42ce-9d94-fd0695e089e7">
											<ee:message>
												<ee:set-payload><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Account has been set to DO_NOT_SYNC"
  }
}]]></ee:set-payload>
											</ee:message>
											<ee:variables>
												<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
											</ee:variables>
										</ee:transform>
					</when>
					<otherwise>
										<logger level="INFO" doc:name="LOG INFO: Non Syncable field updated" doc:id="3e13eaa1-3842-4f30-8740-5c6bd220f7ce" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "A non-syncable field has been updated. NO Sync is required for this account",&#10;	"FlowName": "pf-on-salesforce-account-modified",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
										<ee:transform doc:name="Set payload, httpStatus" doc:id="b55824ad-78a9-4030-a260-37d69c32214f">
											<ee:message>
												<ee:set-payload><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "A non-syncable field has been updated."
  }
}]]></ee:set-payload>
											</ee:message>
											<ee:variables>
												<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
											</ee:variables>
										</ee:transform>
					</otherwise>
				</choice>
							</when>
							<otherwise >
								<logger level="INFO" doc:name="LOG INFO: Record does not exist in REF_ID" doc:id="5d68bcc9-7a2e-4a0d-95c4-948effd3f829" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Record does not exist in REF_ID table",&#10;	"FlowName" : "pf-on-sf-account-modified",&#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
								<ee:transform doc:name="Set payload, httpStatus" doc:id="0fdcbbf2-b6cb-4aff-924f-67c942fa38bf">
									<ee:message>
										<ee:set-payload><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Record does not exist in REF_ID table"
  }
}]]></ee:set-payload>
									</ee:message>
									<ee:variables>
										<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
									</ee:variables>
								</ee:transform>
							</otherwise>
						</choice>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: Discard sync" doc:id="f3e58cb5-ecdf-4c57-99bb-9fd0023cd525" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Account has been updated by Mule flow. NO Sync is required for this contact",&#10;	"FlowName": "pf-on-sf-account-updated",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="c5b226ee-a874-48f2-8c6a-b1ba821ba417" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Account has been created by Mule flow"
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</otherwise>
		</choice>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is disabled" doc:id="057627c7-e714-4844-b6da-c8f576408b10" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForSalesforce is disabled", &#10;	"FlowName" : "pf-on-sf-account-modified", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
				<ee:transform doc:name="Set payload, httpStatus" doc:id="a6fed718-e55d-4065-8b58-673cdeb50ea7" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForSalesforce is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="ff2fb0aa-5987-4c89-b67b-e34eca576518"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-sf-account-modified",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="6318300f-d7a7-458d-9ba6-0e40d6722fa1"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-sf-account-modified",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "Invoked via Salesforce trigger"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="ee11b47f-cc1d-44a1-b992-d7e41aa4692a"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-account-modified",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</sub-flow>
</mule>