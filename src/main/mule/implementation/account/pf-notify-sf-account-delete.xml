<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-notify-sf-account-delete" doc:id="0ad36150-037e-4c67-9a9a-0fc9f822a659" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="964aea15-0da1-4ab5-bac8-a9db03f901fc" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-notify-sf-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="e16a2baa-2ebf-462e-90a0-aa06c50b0601" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-notify-sf-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete"&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="4297a1f1-d8b7-497f-91c7-f0ef7d84025d" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-notify-sf-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vSalesforceId, vObjectType, vRequestAttributes,vSyncPriority" doc:id="9d8b870b-2e2b-41f8-b5c9-c69ef1fd1601" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": "SALESFORCE",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": (now() as LocalDateTime {format: "yyyy-MM-dd'T'HH:mm:ss.000'Z'"}),
		"correlationId": vars.vCorrelationId,
		"sourceId": "SALESFORCE_EXP_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
				<ee:set-variable variableName="vObjectType" ><![CDATA[%dw 2.0
output application/json
---
upper(attributes.queryParams.'objectType')]]></ee:set-variable>
				<ee:set-variable variableName="vSalesforceId" ><![CDATA[attributes.queryParams.'id']]></ee:set-variable>
				<ee:set-variable variableName="vSyncPriority" ><![CDATA[attributes.queryParams.'syncPriority']]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check enableSyncForSalesforce" doc:id="3b33be9d-77b7-46cc-aecf-19ae97692486" >
			<when expression="#[Mule::p('enableSyncForSalesforce') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is enabled" doc:id="abacec98-03c2-4ce3-8ac4-37bdb1f279d8" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForSalesforce is enabled", &#10;	"FlowName" : "pf-notify-sf-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
				<http:request method="GET" doc:name="Fetch ENTERPRISE_ID from REF_ID" doc:id="433ca1ee-e786-4d45-9ba4-e91e7cef0953" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]" target="vRefIdResponse">
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
			<http:query-params><![CDATA[#[output application/java
---
{
	"SALESFORCE_ID": vars.vSalesforceId,
	"OBJECT_TYPE": vars.vObjectType
}]]]></http:query-params>
		</http:request>
				<choice doc:name="Checking Record present in REF_ID table" doc:id="0e0c3e50-943c-418f-80f3-01680993553b">
			<when expression="#[!isEmpty(vars.vRefIdResponse.response)]">
				<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="2c7aa7f6-84e1-4980-ba82-ab48ee6b9fc6">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "DELETE",
    "SOURCE": "SALESFORCE",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID": vars.vRefIdResponse.response[0].'ENTERPRISE_ID', 
    "PAYLOAD": null,
    "PRIORITY": (vars.vSyncPriority default "0") as Number as String {format:"0"} as Number,
    "OBJECT_TYPE": "ACCOUNT",
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
		"val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null
  }
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAIL" doc:id="6e209f74-802c-4fe1-abbe-bd9275e89b00" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionDetails.path')]" target="vInsertTransactionResponse">
			<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
		</http:request>
						<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="52ca50db-1566-4bf0-a962-ebc92eacada2" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')]" target="vSyncRecordResponse">
								<http:body ><![CDATA[#[{}]]]></http:body>
							<http:headers><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
							</http:request>
						<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="bcce0db7-cb7d-426b-8c33-9487870059d2" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-notify-sf-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="dd3c170d-9037-4a1e-bc94-1c2f94741b8a" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "message": "Request for delete notification has been submitted for validation and processing."
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO:Record Not Exits in REF_ID Table" doc:id="8c6b0dbe-92d9-4cb9-8e42-70277f075a52" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Record does not exist in REF_ID table",&#10;	"FlowName" : "pf-notify-sf-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="fdc65634-bc0b-4e12-b4ec-fa5025910fe7" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "message": "Record will not be processed for syncing",
	"description": "Record does not exist in REF_ID table"
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</otherwise>
		</choice>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: enableSyncForSalesforce is disabled" doc:id="db87d93f-b246-4eee-9077-569e9a44d84a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForSalesforce is disabled", &#10;	"FlowName" : "pf-notify-sf-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
				<ee:transform doc:name="Set payload, httpStatus" doc:id="bcbe5e17-0506-4e9d-8657-8fe65171e4c8" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForSalesforce is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="6586abf0-678e-4267-94ea-eb628b877838" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-notify-sf-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="028c22ce-dc05-4543-b3e6-1489e615433d" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-notify-sf-account-delete",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="7d44fc79-1ce5-422c-944d-3b41f1ec26a9" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-notify-sf-account-delete",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
