<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-sf-invoice-updated"
		doc:id="5a1af3f1-606e-4227-9d23-e6a4343e8944" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="c470cdd2-a39b-495a-a94e-d6ecd9887a5c" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-invoice-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": "InvoiceID-"++ vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="371c5788-916b-4fa5-8d23-30f8d7280824"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-invoice-updated&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.invoices.path'),&#10;	&quot;BusinessKey&quot;: &quot;InvoiceID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="295962be-ba1c-4a90-863f-c1f95b3d08b2"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-invoice-updated&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.invoices.path'),&#10;	&quot;BackendRequest&quot;: payload,&#10;	&quot;BusinessKey&quot;: &quot;InvoiceID-&quot;++ vars.vBusinessKey&#10;}]" />

		<ee:transform doc:name="Set payload for order-prc api" doc:id="f7884d6f-3063-485f-9dc6-04313a4cc4ac" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
  "invoice": {
    "netSuiteInvoiceId": payload.invoice.invoiceNSId as Number,
    "approvalStatus": payload.invoice.approvalStatus,
    "comments": payload.invoice.comments,
  	"sfId": payload.invoice.invoiceSFId
  	
  }
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		<http:request method="PUT" doc:name="Call OrderPrc to process invoice" doc:id="01e8ff9e-d83b-46a8-96dc-53f775f803bd" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.invoices.path')]">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="CUSTOM:DATA_VALIDATION_ERROR" />
			<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "salesforce-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vTransactionId,
	"sourceId" : "salesforce-exp-api",
	"x-businessKey" : "InvoiceNetsuiteId-" ++ vars.vBusinessKey
}]]]></http:headers>
							</http:request>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="d9786870-b571-47d5-8779-3872f08dbd9c"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Resonse Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-invoice-updated&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.invoices.path'),&#10;	&quot;BackendResponse&quot;: payload,&#10;	&quot;BusinessKey&quot;: &quot;InvoiceID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="b40b418d-ed54-473f-8c70-d49d2b82d810"
			message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: &quot;Log Outbound Response&quot;,&#10;	&quot;FlowName&quot;: &quot;pf-on-sf-invoice-updated&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.invoices.path'),&#10;	&quot;BusinessKey&quot;: &quot;InvoiceID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="dafb154b-a762-4e40-a180-70af29493315"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-invoice-updated",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": "InvoiceID-"++ vars.vBusinessKey&#10;}]' />
		<error-handler >
			<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="686ab069-5e1b-43d8-a810-506bc53640ec" type="CUSTOM:DATA_VALIDATION_ERROR" >
				<ee:transform doc:name="Set vError" doc:id="a168cc21-994f-4445-9572-232c66fa4a4e" >
					<ee:message />
					<ee:variables >
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
error.'errorMessage'.'payload']]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="969a5d78-0b6e-46c6-8a45-8b0913b47c12" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
vars.vError]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
		</error-handler>
	</flow>

</mule>