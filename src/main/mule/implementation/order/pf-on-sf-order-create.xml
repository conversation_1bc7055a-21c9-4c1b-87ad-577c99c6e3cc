<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-sf-order-create"
		doc:id="0951e4ef-7642-4ad7-b7a8-262771313b6e" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="bacee42d-8c3e-4a1a-bde8-e0312868c68a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-order-created", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": "SalesOrderID-"++ vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="1c09fb42-1661-40f6-adee-2df71f88a582"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-created&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="a2e9ab54-c8f3-4918-a985-af84a2701fb1"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-created&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BackendRequest&quot;: payload,&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />

		<http:request method="POST" doc:name="Call OrderPrc to process order" doc:id="2752dca2-b559-4ad0-9d6c-7c8dcb9ca56d" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.orders.path')]">
							<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="CUSTOM:DATA_VALIDATION_ERROR" />
			<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "Salesforce-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vTransactionId,
	"sourceId" : "Salesforce-exp-api",
	"x-businessKey" : "OrderSalesforceId-" ++ vars.vBusinessKey
}]]]></http:headers>
							</http:request>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="89869a6d-7449-45f3-8106-89f9d73332e8"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Resonse Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-created&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BackendResponse&quot;: payload,&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="bd961831-7cd3-469e-a950-6c8ffa5bdb39"
			message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: &quot;Log Outbound Response&quot;,&#10;	&quot;FlowName&quot;: &quot;pf-on-sf-order-create&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="27611c8d-659c-47c9-9261-02aad14213d2"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-order-created",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": "SalesOrderID-"++ vars.vBusinessKey&#10;}]' />
		<error-handler >
			<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="c8b1d191-d995-4761-af13-ca74b95cf796" type="CUSTOM:DATA_VALIDATION_ERROR">
				<ee:transform doc:name="Set vError" doc:id="ada9b7bc-f2df-447f-a653-9e943dd30eec">
					<ee:message />
					<ee:variables>
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
error.'errorMessage'.'payload']]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="b26b4833-fdf8-42c6-bed0-605bb9c01063" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
vars.vError]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
		</error-handler>
	</flow>

</mule>