<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
<flow name="pf-on-sf-order-delete"
		doc:id="f339971b-0be3-4441-ad0b-db8880755661" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="8f6806f9-1e5f-4772-a628-bf6c9dc83a09" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-sf-order-delete", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": "SalesOrderID-"++ vars.vBusinessKey&#10;}]' />
		<ee:transform
			doc:name="Set vNetsuiteId"
			doc:id="1a8686b9-6a8a-4181-9851-e4adcaeecc6b">
			<ee:variables>
				<ee:set-variable variableName="vNetsuiteId" ><![CDATA[vars.vAttributes.queryParams.orderNetsuiteId]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="e63d8828-24ab-4b87-acc9-8312b7348411"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-delete&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="7c2e72cb-4ebe-4096-aab2-26b552de9fbc"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Request Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-delete&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BackendRequest&quot;: vars.vNetsuiteId,&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />

		<http:request method="DELETE" doc:name="Call OrderPrc to process order" doc:id="d7483e12-dfb9-4ee1-83b7-9ed285464960" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.orders.path')]">
							<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="CUSTOM:DATA_VALIDATION_ERROR" />
			<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "Salesforce-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vTransactionId,
	"sourceId" : "Salesforce-exp-api",
	"x-businessKey" : "OrderNetsuiteId-" ++ vars.vBusinessKey
}]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"orderNetsuiteId" : vars.vNetsuiteId,
	"billingScheduleNetsuiteIds" : attributes.queryParams.billingScheduleNetsuiteIds,
	"billingScheduleDetailNetsuiteIds": attributes.queryParams.billingScheduleDetailNetsuiteIds
}]]]></http:query-params>
							</http:request>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="4227124c-c667-467a-a5ad-36801d05d198"
			message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Outbound Resonse Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-on-sf-order-delete&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BackendResponse&quot;: payload,&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="17c0f60d-dba4-40e9-95c7-d34c9f0b46a4"
			message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: &quot;Log Outbound Response&quot;,&#10;	&quot;FlowName&quot;: &quot;pf-on-sf-order-delete&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: p('https.request.orderPrcApi.orders.path'),&#10;	&quot;BusinessKey&quot;: &quot;SalesOrderID-&quot;++ vars.vBusinessKey&#10;}]" />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="786c1d81-7654-42c3-ace0-0fc9ee48642c"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-sf-order-delete",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": "SalesOrderID-"++ vars.vBusinessKey&#10;}]' />
		<error-handler >
			<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="1e89a5b4-62c2-4841-a91a-c5e7fe303e85" type="CUSTOM:DATA_VALIDATION_ERROR" >
				<ee:transform doc:name="Set vError" doc:id="67cc9648-8d6a-4e2f-a345-a2c0d91860f0" >
					<ee:message />
					<ee:variables >
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
error.'errorMessage'.'payload']]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="a9cdc473-accd-4620-8af9-2b74dafc12a7" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
vars.vError]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
		</error-handler>
	</flow>
</mule>