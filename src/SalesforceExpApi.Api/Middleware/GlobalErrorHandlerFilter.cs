using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SalesforceExpApi.Core.Models.Common;
using SalesforceExpApi.Core.Exceptions;
using SalesforceExpApi.Logging;

namespace SalesforceExpApi.Api.Middleware
{
    /// <summary>
    /// Global error handler equivalent to MuleSoft's global-error-handler
    /// </summary>
    public class GlobalErrorHandlerFilter : IExceptionFilter
    {
        private readonly ILogger<GlobalErrorHandlerFilter> _logger;
        private readonly ICorrelationService _correlationService;

        public GlobalErrorHandlerFilter(ILogger<GlobalErrorHandlerFilter> logger, ICorrelationService correlationService)
        {
            _logger = logger;
            _correlationService = correlationService;
        }

        public void OnException(ExceptionContext context)
        {
            var correlationId = _correlationService.GetCorrelationId();
            var httpStatus = 500;
            var errorMessage = "INTERNAL_SERVER_ERROR";
            var errorDetails = context.Exception.Message;
            var detailedDescription = context.Exception.ToString();

            // Map specific exception types to appropriate HTTP status codes and error messages
            switch (context.Exception)
            {
                case ArgumentNullException _:
                case ArgumentException _:
                    httpStatus = 400;
                    errorMessage = "BAD_REQUEST";
                    break;

                case UnauthorizedAccessException _:
                    httpStatus = 401;
                    errorMessage = "UNAUTHORIZED";
                    break;

                case KeyNotFoundException _:
                    httpStatus = 404;
                    errorMessage = "NOT_FOUND";
                    break;

                case TimeoutException _:
                    httpStatus = 408;
                    errorMessage = "REQUEST_TIMEOUT";
                    break;

                case DataValidationException _:
                    httpStatus = 400;
                    errorMessage = "DATA_VALIDATION_ERROR";
                    break;

                case ExternalServiceException externalEx:
                    httpStatus = 502;
                    errorMessage = "EXTERNAL_SERVICE_ERROR";
                    errorDetails = externalEx.ServiceName + ": " + externalEx.Message;
                    break;

                case DataWeaveTransformationException _:
                    httpStatus = 500;
                    errorMessage = "DATAWEAVE_EXPRESSION_ERROR";
                    break;

                case RecordDependencyException _:
                    httpStatus = 409;
                    errorMessage = "CAN_NOT_DELETE";
                    break;

                default:
                    httpStatus = 500;
                    errorMessage = "INTERNAL_SERVER_ERROR";
                    break;
            }

            // Log the error with appropriate level
            if (httpStatus >= 500)
            {
                _logger.LogError(context.Exception, 
                    "Global error handler caught exception: {ErrorMessage}, CorrelationID: {CorrelationId}, HttpStatus: {HttpStatus}",
                    errorMessage, correlationId, httpStatus);
            }
            else
            {
                _logger.LogWarning(context.Exception,
                    "Global error handler caught client error: {ErrorMessage}, CorrelationID: {CorrelationId}, HttpStatus: {HttpStatus}",
                    errorMessage, correlationId, httpStatus);
            }

            // Create standardized error response
            var errorResponse = new ErrorResponse
            {
                Code = httpStatus,
                Status = "FAILURE",
                TransactionId = correlationId,
                Response = new ErrorDetails
                {
                    Message = errorMessage,
                    Details = errorDetails,
                    DetailedDescription = detailedDescription
                }
            };

            context.Result = new ObjectResult(errorResponse)
            {
                StatusCode = httpStatus
            };

            context.ExceptionHandled = true;
        }
    }

    /// <summary>
    /// Middleware for handling HTTP-specific errors
    /// </summary>
    public class HttpErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<HttpErrorHandlingMiddleware> _logger;

        public HttpErrorHandlingMiddleware(RequestDelegate next, ILogger<HttpErrorHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);

                // Handle specific HTTP status codes that weren't caught by exceptions
                if (context.Response.StatusCode == 404 && !context.Response.HasStarted)
                {
                    await HandleNotFoundAsync(context);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unhandled exception in HTTP pipeline");
                
                if (!context.Response.HasStarted)
                {
                    await HandleExceptionAsync(context, ex);
                }
            }
        }

        private async Task HandleNotFoundAsync(HttpContext context)
        {
            var correlationId = context.Items["CorrelationId"]?.ToString() ?? Guid.NewGuid().ToString();
            
            var errorResponse = new ErrorResponse
            {
                Code = 404,
                Status = "FAILURE",
                TransactionId = correlationId,
                Response = new ErrorDetails
                {
                    Message = "NOT_FOUND",
                    Details = $"The requested resource '{context.Request.Path}' was not found.",
                    DetailedDescription = null
                }
            };

            context.Response.StatusCode = 404;
            context.Response.ContentType = "application/json";
            
            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(errorResponse));
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            var correlationId = context.Items["CorrelationId"]?.ToString() ?? Guid.NewGuid().ToString();
            
            var errorResponse = new ErrorResponse
            {
                Code = 500,
                Status = "FAILURE",
                TransactionId = correlationId,
                Response = new ErrorDetails
                {
                    Message = "INTERNAL_SERVER_ERROR",
                    Details = exception.Message,
                    DetailedDescription = exception.ToString()
                }
            };

            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";
            
            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(errorResponse));
        }
    }

    /// <summary>
    /// Extension methods for registering error handling
    /// </summary>
    public static class ErrorHandlingExtensions
    {
        public static IServiceCollection AddGlobalErrorHandling(this IServiceCollection services)
        {
            services.AddScoped<GlobalErrorHandlerFilter>();
            return services;
        }

        public static IApplicationBuilder UseGlobalErrorHandling(this IApplicationBuilder app)
        {
            app.UseMiddleware<HttpErrorHandlingMiddleware>();
            return app;
        }
    }
}
