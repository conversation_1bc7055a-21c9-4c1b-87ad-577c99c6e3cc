using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace SalesforceExpApi.Api.Configuration
{
    public static class AuthenticationConfiguration
    {
        public static IServiceCollection AddAuthenticationConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            // Add JWT Bearer authentication
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    var jwtSettings = configuration.GetSection("Authentication:Jwt");
                    
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = jwtSettings["Issuer"],
                        ValidAudience = jwtSettings["Audience"],
                        IssuerSigningKey = new SymmetricSecurityKey(
                            Encoding.UTF8.GetBytes(jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey is required")))
                    };

                    options.Events = new JwtBearerEvents
                    {
                        OnAuthenticationFailed = context =>
                        {
                            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                            logger.LogError(context.Exception, "JWT Authentication failed");
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = context =>
                        {
                            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                            logger.LogDebug("JWT Token validated for user: {User}", context.Principal?.Identity?.Name);
                            return Task.CompletedTask;
                        }
                    };
                });

            // Add authorization policies
            services.AddAuthorization(options =>
            {
                // Client ID enforcement policy
                options.AddPolicy("ClientIdEnforcement", policy =>
                {
                    policy.RequireAssertion(context =>
                    {
                        var httpContext = context.Resource as HttpContext;
                        if (httpContext == null) return false;

                        var clientIdEnforcementSettings = configuration.GetSection("Authentication:ClientIdEnforcement");
                        var requireClientId = clientIdEnforcementSettings.GetValue<bool>("RequireClientId", true);
                        
                        if (!requireClientId) return true;

                        var clientId = httpContext.Request.Headers["client_id"].FirstOrDefault();
                        if (string.IsNullOrEmpty(clientId)) return false;

                        var validClientIds = clientIdEnforcementSettings.GetSection("ValidClientIds").Get<string[]>() ?? Array.Empty<string>();
                        return validClientIds.Contains(clientId);
                    });
                });

                // Default policy requires authentication
                options.DefaultPolicy = options.GetPolicy("ClientIdEnforcement") ?? throw new InvalidOperationException("Default policy not found");
            });

            // Add custom authentication middleware
            services.AddScoped<ClientIdAuthenticationMiddleware>();

            return services;
        }
    }

    /// <summary>
    /// Middleware to handle client ID authentication
    /// </summary>
    public class ClientIdAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ClientIdAuthenticationMiddleware> _logger;

        public ClientIdAuthenticationMiddleware(
            RequestDelegate next, 
            IConfiguration configuration, 
            ILogger<ClientIdAuthenticationMiddleware> logger)
        {
            _next = next;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip authentication for health check endpoints
            if (context.Request.Path.StartsWithSegments("/health"))
            {
                await _next(context);
                return;
            }

            var clientIdEnforcementSettings = _configuration.GetSection("Authentication:ClientIdEnforcement");
            var requireClientId = clientIdEnforcementSettings.GetValue<bool>("RequireClientId", true);

            if (requireClientId)
            {
                var clientId = context.Request.Headers["client_id"].FirstOrDefault();
                
                if (string.IsNullOrEmpty(clientId))
                {
                    _logger.LogWarning("Request missing client_id header from {RemoteIpAddress}", context.Connection.RemoteIpAddress);
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsync("Missing client_id header");
                    return;
                }

                var validClientIds = clientIdEnforcementSettings.GetSection("ValidClientIds").Get<string[]>() ?? Array.Empty<string>();
                
                if (!validClientIds.Contains(clientId))
                {
                    _logger.LogWarning("Invalid client_id: {ClientId} from {RemoteIpAddress}", clientId, context.Connection.RemoteIpAddress);
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsync("Invalid client_id");
                    return;
                }

                _logger.LogDebug("Valid client_id: {ClientId}", clientId);
            }

            await _next(context);
        }
    }
}
