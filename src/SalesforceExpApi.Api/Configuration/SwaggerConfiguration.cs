using Microsoft.OpenApi.Models;
using System.Reflection;

namespace SalesforceExpApi.Api.Configuration
{
    public static class SwaggerConfiguration
    {
        public static IServiceCollection AddSwaggerConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = configuration["Swagger:Title"] ?? "Salesforce Experience API",
                    Version = configuration["Swagger:Version"] ?? "v1",
                    Description = configuration["Swagger:Description"] ?? "API for Salesforce data synchronization and processing",
                    Contact = new OpenApiContact
                    {
                        Name = configuration["Swagger:ContactName"] ?? "API Support",
                        Email = configuration["Swagger:ContactEmail"] ?? "<EMAIL>"
                    }
                });

                // Add security definition for Client ID enforcement
                c.AddSecurityDefinition("ClientId", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.ApiKey,
                    In = ParameterLocation.Header,
                    Name = "client_id",
                    Description = "Client ID for API access"
                });

                // Add security definition for JWT Bearer
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT",
                    Description = "JWT Authorization header using the Bearer scheme"
                });

                // Add security requirements
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "ClientId"
                            }
                        },
                        Array.Empty<string>()
                    },
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });

                // Include XML comments
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }

                // Add custom headers
                c.OperationFilter<CorrelationIdHeaderOperationFilter>();
            });

            return services;
        }
    }

    public class CorrelationIdHeaderOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            operation.Parameters ??= new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "correlationId",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string"
                },
                Description = "Correlation ID for request tracking"
            });

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "x-transactionId",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string"
                },
                Description = "Transaction ID for request tracking"
            });

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "x-source",
                In = ParameterLocation.Header,
                Required = false,
                Schema = new OpenApiSchema
                {
                    Type = "string"
                },
                Description = "Source system identifier"
            });
        }
    }
}
