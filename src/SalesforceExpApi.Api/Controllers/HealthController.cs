using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace SalesforceExpApi.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly HealthCheckService _healthCheckService;
        private readonly ILogger<HealthController> _logger;

        public HealthController(HealthCheckService healthCheckService, ILogger<HealthController> logger)
        {
            _healthCheckService = healthCheckService;
            _logger = logger;
        }

        /// <summary>
        /// Get the health status of the application
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(HealthCheckResult), 200)]
        [ProducesResponseType(typeof(HealthCheckResult), 503)]
        public async Task<IActionResult> Get()
        {
            var report = await _healthCheckService.CheckHealthAsync();

            _logger.LogInformation("Health check executed. Status: {Status}", report.Status);

            return report.Status == HealthStatus.Healthy ? Ok(report) : StatusCode(503, report);
        }

        /// <summary>
        /// Get detailed health status of all dependencies
        /// </summary>
        [HttpGet("detailed")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(typeof(object), 503)]
        public async Task<IActionResult> GetDetailed()
        {
            var report = await _healthCheckService.CheckHealthAsync();

            var response = new
            {
                Status = report.Status.ToString(),
                TotalDuration = report.TotalDuration,
                Entries = report.Entries.Select(e => new
                {
                    Name = e.Key,
                    Status = e.Value.Status.ToString(),
                    Duration = e.Value.Duration,
                    Description = e.Value.Description,
                    Data = e.Value.Data
                })
            };

            _logger.LogInformation("Detailed health check executed. Status: {Status}, Duration: {Duration}ms", 
                report.Status, report.TotalDuration.TotalMilliseconds);

            return report.Status == HealthStatus.Healthy ? Ok(response) : StatusCode(503, response);
        }
    }
}
