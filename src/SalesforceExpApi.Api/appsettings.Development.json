{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Warning", "SalesforceExpApi": "Debug"}}, "HttpClients": {"TransactionDbApi": {"Host": "localhost", "Port": 8081, "ConnectionTimeout": 30000, "ResponseTimeout": 30000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "TruststorePath": "", "TruststorePassword": ""}, "SyncPrcApi": {"Host": "localhost", "Port": 8082, "BasePath": "/", "ConnectionTimeout": 30000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "ClientId": "dev-client-id", "ClientSecret": "dev-client-secret", "TruststorePath": "", "TruststorePassword": ""}, "OrderPrcApi": {"Host": "localhost", "Port": 8083, "BasePath": "/", "ConnectionTimeout": 30000, "ResponseTimeout": 185000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "ClientId": "dev-client-id", "ClientSecret": "dev-client-secret", "TruststorePath": "", "TruststorePassword": ""}}, "Salesforce": {"ConsumerKey": "dev-consumer-key", "ConsumerSecret": "dev-consumer-secret", "Username": "dev-username", "Password": "dev-password", "SecurityToken": "dev-security-token", "LoginUrl": "https://test.salesforce.com", "User": {"MulesoftId": "dev-mulesoft-user-id"}}, "Authentication": {"ClientIdEnforcement": {"RequireClientId": false, "ValidClientIds": ["dev-client-1", "dev-client-2"]}, "Jwt": {"SecretKey": "dev-jwt-secret-key-for-development-only-make-it-long-and-secure"}}, "EnableSyncForSalesforce": "1", "HealthChecks": {"Endpoints": {"TransactionDbApi": "http://localhost:8081/health", "SyncPrcApi": "http://localhost:8082/health", "OrderPrcApi": "http://localhost:8083/health", "Salesforce": "https://test.salesforce.com"}}}