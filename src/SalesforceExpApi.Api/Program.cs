using SalesforceExpApi.Api.Configuration;
using SalesforceExpApi.Api.Middleware;
using SalesforceExpApi.Core.Extensions;
using SalesforceExpApi.Infrastructure.Extensions;
using SalesforceExpApi.Logging.Extensions;
using SalesforceExpApi.EventProcessing.Extensions;
using Serilog;
using Microsoft.AspNetCore.RateLimiting;
using System.Threading.RateLimiting;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers(options =>
{
    options.Filters.Add<GlobalErrorHandlerFilter>();
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerConfiguration(builder.Configuration);

// Add authentication and authorization
builder.Services.AddAuthenticationConfiguration(builder.Configuration);

// Add HTTP context accessor for correlation service
builder.Services.AddHttpContextAccessor();

// Add custom services
builder.Services.AddCoreServices();
builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddMuleSoftLogging();
builder.Services.AddEventProcessingServices(builder.Configuration);
builder.Services.AddGlobalErrorHandling();

// Add health checks
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy())
    .AddUrlGroup(new Uri(builder.Configuration["HealthChecks:Endpoints:TransactionDbApi"] ?? "https://localhost"), "TransactionDbApi")
    .AddUrlGroup(new Uri(builder.Configuration["HealthChecks:Endpoints:SyncPrcApi"] ?? "https://localhost"), "SyncPrcApi")
    .AddUrlGroup(new Uri(builder.Configuration["HealthChecks:Endpoints:OrderPrcApi"] ?? "https://localhost"), "OrderPrcApi");

// Add rate limiting
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("ApiRateLimit", limiterOptions =>
    {
        limiterOptions.PermitLimit = builder.Configuration.GetValue<int>("RateLimiting:PermitLimit", 100);
        limiterOptions.Window = TimeSpan.Parse(builder.Configuration["RateLimiting:Window"] ?? "00:01:00");
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = builder.Configuration.GetValue<int>("RateLimiting:QueueLimit", 50);
    });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? Array.Empty<string>();
        var allowedMethods = builder.Configuration.GetSection("Cors:AllowedMethods").Get<string[]>() ?? Array.Empty<string>();
        var allowedHeaders = builder.Configuration.GetSection("Cors:AllowedHeaders").Get<string[]>() ?? Array.Empty<string>();

        policy.WithOrigins(allowedOrigins)
              .WithMethods(allowedMethods)
              .WithHeaders(allowedHeaders)
              .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Salesforce Experience API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

// Use HTTPS redirection
app.UseHttpsRedirection();

// Use CORS
app.UseCors();

// Use rate limiting
app.UseRateLimiter();

// Use custom middleware
app.UseGlobalErrorHandling();
app.UseMiddleware<CorrelationIdMiddleware>();

// Use authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

// Map controllers
app.MapControllers();

// Map health checks
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready");
app.MapHealthChecks("/health/live");

try
{
    Log.Information("Starting Salesforce Experience API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
