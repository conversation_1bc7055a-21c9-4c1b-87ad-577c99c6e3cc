using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SalesforceExpApi.EventProcessing.Services;
using SalesforceExpApi.EventProcessing.Handlers;

namespace SalesforceExpApi.EventProcessing.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddEventProcessingServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Register the background service
            services.AddHostedService<SalesforceEventProcessingService>();

            // Register event handlers
            services.AddScoped<IAccountEventHandler, AccountEventHandler>();
            services.AddScoped<IProductEventHandler, ProductEventHandler>();
            services.AddScoped<IOpportunityEventHandler, OpportunityEventHandler>();

            // Register Salesforce event client (placeholder implementation)
            services.AddScoped<ISalesforceEventClient, SalesforceEventClient>();

            return services;
        }
    }
}
