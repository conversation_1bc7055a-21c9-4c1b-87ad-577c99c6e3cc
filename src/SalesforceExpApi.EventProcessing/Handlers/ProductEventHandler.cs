using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SalesforceExpApi.Logging;
using SalesforceExpApi.EventProcessing.Models;
using SalesforceExpApi.EventProcessing.Handlers;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;
using SalesforceExpApi.Core.Services.Interfaces;

namespace SalesforceExpApi.EventProcessing.Handlers
{
    public class ProductEventHandler : IProductEventHandler
    {
        private readonly ILogger<ProductEventHandler> _logger;
        private readonly IMuleSoftLogger _muleSoftLogger;
        private readonly ITransactionDbApiClient _transactionDbClient;
        private readonly ISyncPrcApiClient _syncPrcClient;
        private readonly IConfiguration _configuration;
        private readonly IDataTransformationService _transformationService;

        public ProductEventHandler(
            ILogger<ProductEventHandler> logger,
            IMuleSoftLogger muleSoftLogger,
            ITransactionDbApiClient transactionDbClient,
            ISyncPrcApiClient syncPrcClient,
            IConfiguration configuration,
            IDataTransformationService transformationService)
        {
            _logger = logger;
            _muleSoftLogger = muleSoftLogger;
            _transactionDbClient = transactionDbClient;
            _syncPrcClient = syncPrcClient;
            _configuration = configuration;
            _transformationService = transformationService;
        }

        public async Task HandleProductCreatedAsync(SalesforceObject product)
        {
            var correlationId = Guid.NewGuid().ToString();

            _muleSoftLogger.LogFlowStart("pf-on-sf-product-created", correlationId);

            try
            {
                // Check if sync is required
                if (!ShouldSyncProduct(product))
                {
                    _muleSoftLogger.LogSyncStatus("pf-on-sf-product-created", correlationId, "IGNORED", 
                        GetIgnoreReason(product));
                    return;
                }

                // Call sync products API
                await _syncPrcClient.SyncProductsAsync(correlationId);

                _muleSoftLogger.LogFlowEnd("pf-on-sf-product-created", correlationId);
            }
            catch (Exception ex)
            {
                _muleSoftLogger.LogCustomMessage(LogLevel.Error, $"Error in product created flow: {ex.Message}", 
                    "pf-on-sf-product-created", correlationId);
                throw;
            }
        }

        public async Task HandleProductModifiedAsync(SalesforceObject product)
        {
            var correlationId = Guid.NewGuid().ToString();

            _muleSoftLogger.LogFlowStart("pf-on-sf-product-modified", correlationId);

            try
            {
                // Check if record was recently created
                if (IsRecentlyCreated(product))
                {
                    _muleSoftLogger.LogSyncStatus("pf-on-sf-product-modified", correlationId, "IGNORED", 
                        "Record was recently created");
                    return;
                }

                // Check sync requirements
                if (!ShouldSyncProduct(product))
                {
                    _muleSoftLogger.LogSyncStatus("pf-on-sf-product-modified", correlationId, "IGNORED", 
                        GetIgnoreReason(product));
                    return;
                }

                // Call sync products API
                await _syncPrcClient.SyncProductsAsync(correlationId);

                _muleSoftLogger.LogFlowEnd("pf-on-sf-product-modified", correlationId);
            }
            catch (Exception ex)
            {
                _muleSoftLogger.LogCustomMessage(LogLevel.Error, $"Error in product modified flow: {ex.Message}", 
                    "pf-on-sf-product-modified", correlationId);
                throw;
            }
        }

        private bool ShouldSyncProduct(SalesforceObject product)
        {
            var enableSyncForSalesforce = _configuration.GetValue<string>("EnableSyncForSalesforce");
            if (enableSyncForSalesforce != "1") return false;

            var mulesoftUserId = _configuration.GetValue<string>("Salesforce:User:MulesoftId");
            var lastModifiedById = product.GetField<string>("LastModifiedById");
            var doNotSync = product.GetField<bool>("DoNotSync__c");

            return lastModifiedById != mulesoftUserId && !doNotSync;
        }

        private string GetIgnoreReason(SalesforceObject product)
        {
            var mulesoftUserId = _configuration.GetValue<string>("Salesforce:User:MulesoftId");
            var lastModifiedById = product.GetField<string>("LastModifiedById");
            var doNotSync = product.GetField<bool>("DoNotSync__c");

            if (lastModifiedById == mulesoftUserId)
                return "Product has been created by Mule flow";
            if (doNotSync)
                return "Product has been set to DO_NOT_SYNC";
            
            return "Unknown reason";
        }

        private bool IsRecentlyCreated(SalesforceObject product)
        {
            var lastModifiedDate = product.GetField<DateTime>("LastModifiedDate");
            var createdDate = product.GetField<DateTime>("CreatedDate");
            var createTimeDiff = _configuration.GetValue<int>("Salesforce:CreateTimeDiff", 300);

            var timeDifference = Math.Abs((lastModifiedDate - createdDate).TotalSeconds);
            return timeDifference <= createTimeDiff;
        }
    }
}
