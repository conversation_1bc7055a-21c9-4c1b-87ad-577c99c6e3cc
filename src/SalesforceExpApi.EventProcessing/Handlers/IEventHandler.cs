using SalesforceExpApi.EventProcessing.Models;

namespace SalesforceExpApi.EventProcessing.Handlers
{
    /// <summary>
    /// Event handlers for different Salesforce objects
    /// </summary>
    public interface IAccountEventHandler
    {
        Task HandleAccountCreatedAsync(SalesforceObject account);
        Task HandleAccountModifiedAsync(SalesforceObject account);
    }

    public interface IProductEventHandler
    {
        Task HandleProductCreatedAsync(SalesforceObject product);
        Task HandleProductModifiedAsync(SalesforceObject product);
    }

    public interface IOpportunityEventHandler
    {
        Task HandleOpportunityModifiedAsync(SalesforceEvent opportunityEvent);
    }
}
