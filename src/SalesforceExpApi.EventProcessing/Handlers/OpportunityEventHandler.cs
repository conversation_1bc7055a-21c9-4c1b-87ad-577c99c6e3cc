using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SalesforceExpApi.Logging;
using SalesforceExpApi.EventProcessing.Models;
using SalesforceExpApi.EventProcessing.Handlers;
using SalesforceExpApi.Infrastructure.HttpClients.Interfaces;
using SalesforceExpApi.Core.Services.Interfaces;

namespace SalesforceExpApi.EventProcessing.Handlers
{
    public class OpportunityEventHandler : IOpportunityEventHandler
    {
        private readonly ILogger<OpportunityEventHandler> _logger;
        private readonly IMuleSoftLogger _muleSoftLogger;
        private readonly IOrderPrcApiClient _orderPrcClient;
        private readonly IConfiguration _configuration;
        private readonly IDataTransformationService _transformationService;

        public OpportunityEventHandler(
            ILogger<OpportunityEventHandler> logger,
            IMuleSoftLogger muleSoftLogger,
            IOrderPrcApiClient orderPrcClient,
            IConfiguration configuration,
            IDataTransformationService transformationService)
        {
            _logger = logger;
            _muleSoftLogger = muleSoftLogger;
            _orderPrcClient = orderPrcClient;
            _configuration = configuration;
            _transformationService = transformationService;
        }

        public async Task HandleOpportunityModifiedAsync(SalesforceEvent opportunityEvent)
        {
            var correlationId = Guid.NewGuid().ToString();

            _muleSoftLogger.LogFlowStart("pf-on-sf-opportunity-modified", correlationId);

            try
            {
                // Extract opportunity data from event
                var opportunityData = ExtractOpportunityData(opportunityEvent);
                
                if (opportunityData == null)
                {
                    _muleSoftLogger.LogSyncStatus("pf-on-sf-opportunity-modified", correlationId, "IGNORED", 
                        "No opportunity data found in event");
                    return;
                }

                // Validate required fields
                var salesforceOpportunityId = GetFieldValue(opportunityData, "Salesforce_OpportunityID__c");
                var artemisConfirmId = GetFieldValue(opportunityData, "Artemis_ConfirmID__c");

                if (string.IsNullOrEmpty(salesforceOpportunityId) || string.IsNullOrEmpty(artemisConfirmId))
                {
                    _muleSoftLogger.LogSyncStatus("pf-on-sf-opportunity-modified", correlationId, "IGNORED", 
                        "Missing required opportunity fields");
                    return;
                }

                var businessKey = $"OpportunityID-{salesforceOpportunityId}";

                // Transform opportunity data for Order Process API
                var transformedData = TransformOpportunityData(opportunityData);

                // Call Order Process API
                await _orderPrcClient.ProcessOpportunityAsync(transformedData, "PUT", correlationId, businessKey);

                _muleSoftLogger.LogFlowEnd("pf-on-sf-opportunity-modified", correlationId);
            }
            catch (Exception ex)
            {
                _muleSoftLogger.LogCustomMessage(LogLevel.Error, $"Error in opportunity modified flow: {ex.Message}", 
                    "pf-on-sf-opportunity-modified", correlationId);
                throw;
            }
        }

        private object? ExtractOpportunityData(SalesforceEvent opportunityEvent)
        {
            try
            {
                // Extract opportunity data from the event payload
                if (opportunityEvent.Payload.TryGetValue("opportunity", out var opportunityData))
                {
                    return opportunityData;
                }

                // Fallback to direct data if no nested structure
                return opportunityEvent.Data.Any() ? opportunityEvent.Data : null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract opportunity data from event");
                return null;
            }
        }

        private string? GetFieldValue(object data, string fieldName)
        {
            try
            {
                if (data is IDictionary<string, object> dict)
                {
                    return dict.TryGetValue(fieldName, out var value) ? value?.ToString() : null;
                }

                // Try reflection for strongly typed objects
                var property = data.GetType().GetProperty(fieldName);
                return property?.GetValue(data)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        private object TransformOpportunityData(object opportunityData)
        {
            // Transform the opportunity data to match the expected format for Order Process API
            // This is equivalent to the DataWeave transformation in the original flow
            
            try
            {
                if (opportunityData is IDictionary<string, object> dict)
                {
                    return new
                    {
                        opportunity = new
                        {
                            salesforceOpportunityId = GetFieldValue(opportunityData, "Salesforce_OpportunityID__c"),
                            artemisConfirmId = GetFieldValue(opportunityData, "Artemis_ConfirmID__c"),
                            name = GetFieldValue(opportunityData, "Name"),
                            stageName = GetFieldValue(opportunityData, "StageName"),
                            amount = GetFieldValue(opportunityData, "Amount"),
                            closeDate = GetFieldValue(opportunityData, "CloseDate"),
                            lastModifiedDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                        }
                    };
                }

                // Return as-is if transformation fails
                return opportunityData;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to transform opportunity data, returning original");
                return opportunityData;
            }
        }
    }
}
