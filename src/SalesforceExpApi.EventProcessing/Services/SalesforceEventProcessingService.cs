using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using SalesforceExpApi.Logging;
using SalesforceExpApi.EventProcessing.Models;
using SalesforceExpApi.EventProcessing.Handlers;

namespace SalesforceExpApi.EventProcessing.Services
{
    /// <summary>
    /// Background service to handle Salesforce events equivalent to MuleSoft listeners
    /// Replaces: salesforce:new-object-listener, salesforce:modified-object-listener, salesforce:subscribe-channel-listener
    /// </summary>
    public class SalesforceEventProcessingService : BackgroundService
    {
        private readonly ILogger<SalesforceEventProcessingService> _logger;
        private readonly IMuleSoftLogger _muleSoftLogger;
        private readonly IConfiguration _configuration;
        private readonly ISalesforceEventClient _salesforceClient;
        private readonly IAccountEventHandler _accountEventHandler;
        private readonly IProductEventHandler _productEventHandler;
        private readonly IOpportunityEventHandler _opportunityEventHandler;

        public SalesforceEventProcessingService(
            ILogger<SalesforceEventProcessingService> logger,
            IMuleSoftLogger muleSoftLogger,
            IConfiguration configuration,
            ISalesforceEventClient salesforceClient,
            IAccountEventHandler accountEventHandler,
            IProductEventHandler productEventHandler,
            IOpportunityEventHandler opportunityEventHandler)
        {
            _logger = logger;
            _muleSoftLogger = muleSoftLogger;
            _configuration = configuration;
            _salesforceClient = salesforceClient;
            _accountEventHandler = accountEventHandler;
            _productEventHandler = productEventHandler;
            _opportunityEventHandler = opportunityEventHandler;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Salesforce Event Processing Service started");

            // Start multiple event listeners concurrently
            var tasks = new List<Task>
            {
                ListenForAccountEvents(stoppingToken),
                ListenForProductEvents(stoppingToken),
                ListenForOpportunityEvents(stoppingToken)
            };

            await Task.WhenAll(tasks);
        }

        private async Task ListenForAccountEvents(CancellationToken cancellationToken)
        {
            var pollFrequency = TimeSpan.FromMilliseconds(
                _configuration.GetValue<int>("Salesforce:Poll:Frequency", 5000));
            var startDelay = TimeSpan.FromMilliseconds(
                _configuration.GetValue<int>("Salesforce:Poll:StartDelay", 1000));

            await Task.Delay(startDelay, cancellationToken);

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if account flows are enabled
                    var accountCreatedFlowState = _configuration.GetValue<string>("FlowStates:AccountCreatedFlowState", "started");
                    var accountModifiedFlowState = _configuration.GetValue<string>("FlowStates:AccountModifiedFlowState", "started");

                    if (accountCreatedFlowState == "started")
                    {
                        await ProcessNewAccountRecords(cancellationToken);
                    }

                    if (accountModifiedFlowState == "started")
                    {
                        await ProcessModifiedAccountRecords(cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing account events");
                }

                await Task.Delay(pollFrequency, cancellationToken);
            }
        }

        private async Task ListenForProductEvents(CancellationToken cancellationToken)
        {
            var pollFrequency = TimeSpan.FromMilliseconds(
                _configuration.GetValue<int>("Salesforce:Poll:Frequency", 5000));
            var startDelay = TimeSpan.FromMilliseconds(
                _configuration.GetValue<int>("Salesforce:Poll:StartDelay", 1000));

            await Task.Delay(startDelay, cancellationToken);

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var productCreatedFlowState = _configuration.GetValue<string>("FlowStates:ProductCreatedFlowState", "started");
                    var productModifiedFlowState = _configuration.GetValue<string>("FlowStates:ProductModifiedFlowState", "started");

                    if (productCreatedFlowState == "started")
                    {
                        await ProcessNewProductRecords(cancellationToken);
                    }

                    if (productModifiedFlowState == "started")
                    {
                        await ProcessModifiedProductRecords(cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing product events");
                }

                await Task.Delay(pollFrequency, cancellationToken);
            }
        }

        private async Task ListenForOpportunityEvents(CancellationToken cancellationToken)
        {
            var channelName = _configuration.GetValue<string>("Salesforce:Channel:OpportunityChangeEvent:Name", "/event/Ready_For_Fulfillment__e");

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    await _salesforceClient.SubscribeToChannelAsync(channelName, async (eventData) =>
                    {
                        await _opportunityEventHandler.HandleOpportunityModifiedAsync(eventData);
                    }, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing opportunity events from channel {ChannelName}", channelName);
                    await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken); // Wait before retrying
                }
            }
        }

        private async Task ProcessNewAccountRecords(CancellationToken cancellationToken)
        {
            var newAccounts = await _salesforceClient.GetNewObjectsAsync("Account", cancellationToken);
            
            foreach (var account in newAccounts)
            {
                try
                {
                    await _accountEventHandler.HandleAccountCreatedAsync(account);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing new account record {AccountId}", account?.Id);
                }
            }
        }

        private async Task ProcessModifiedAccountRecords(CancellationToken cancellationToken)
        {
            var modifiedAccounts = await _salesforceClient.GetModifiedObjectsAsync("Account", cancellationToken);
            
            foreach (var account in modifiedAccounts)
            {
                try
                {
                    await _accountEventHandler.HandleAccountModifiedAsync(account);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing modified account record {AccountId}", account?.Id);
                }
            }
        }

        private async Task ProcessNewProductRecords(CancellationToken cancellationToken)
        {
            var newProducts = await _salesforceClient.GetNewObjectsAsync("Product2", cancellationToken);
            
            foreach (var product in newProducts)
            {
                try
                {
                    await _productEventHandler.HandleProductCreatedAsync(product);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing new product record {ProductId}", product?.Id);
                }
            }
        }

        private async Task ProcessModifiedProductRecords(CancellationToken cancellationToken)
        {
            var modifiedProducts = await _salesforceClient.GetModifiedObjectsAsync("Product2", cancellationToken);
            
            foreach (var product in modifiedProducts)
            {
                try
                {
                    await _productEventHandler.HandleProductModifiedAsync(product);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing modified product record {ProductId}", product?.Id);
                }
            }
        }
    }

    /// <summary>
    /// Interface for Salesforce event client
    /// </summary>
    public interface ISalesforceEventClient
    {
        Task<IEnumerable<SalesforceObject>> GetNewObjectsAsync(string objectType, CancellationToken cancellationToken);
        Task<IEnumerable<SalesforceObject>> GetModifiedObjectsAsync(string objectType, CancellationToken cancellationToken);
        Task SubscribeToChannelAsync(string channelName, Func<SalesforceEvent, Task> eventHandler, CancellationToken cancellationToken);
    }
}
