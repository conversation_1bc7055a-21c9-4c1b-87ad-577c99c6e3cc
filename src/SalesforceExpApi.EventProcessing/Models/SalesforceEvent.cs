namespace SalesforceExpApi.EventProcessing.Models
{
    public class SalesforceEvent
    {
        public string Id { get; set; } = string.Empty;
        public string EventType { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string CreatedById { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
        public Dictionary<string, object> Payload { get; set; } = new();
    }

    public class SalesforceObject
    {
        public string Id { get; set; } = string.Empty;
        public string ObjectType { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string? CreatedById { get; set; }
        public string? LastModifiedById { get; set; }
        public bool IsDeleted { get; set; }
        public Dictionary<string, object> Fields { get; set; } = new();

        public T? GetField<T>(string fieldName)
        {
            if (Fields.TryGetValue(fieldName, out var value))
            {
                try
                {
                    if (value is T directValue)
                        return directValue;

                    if (typeof(T) == typeof(string))
                        return (T)(object)(value?.ToString() ?? string.Empty);

                    if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                    {
                        if (bool.TryParse(value?.ToString(), out var boolValue))
                            return (T)(object)boolValue;
                    }

                    if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                    {
                        if (DateTime.TryParse(value?.ToString(), out var dateValue))
                            return (T)(object)dateValue;
                    }

                    if (typeof(T) == typeof(int) || typeof(T) == typeof(int?))
                    {
                        if (int.TryParse(value?.ToString(), out var intValue))
                            return (T)(object)intValue;
                    }

                    if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                    {
                        if (decimal.TryParse(value?.ToString(), out var decimalValue))
                            return (T)(object)decimalValue;
                    }

                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return default(T);
                }
            }

            return default(T);
        }

        public void SetField(string fieldName, object? value)
        {
            Fields[fieldName] = value ?? string.Empty;
        }
    }

    public class AccountEvent : SalesforceObject
    {
        public AccountEvent()
        {
            ObjectType = "Account";
        }

        public string? AccountID__c
        {
            get => GetField<string>("AccountID__c");
            set => SetField("AccountID__c", value);
        }

        public string Name
        {
            get => GetField<string>("Name") ?? string.Empty;
            set => SetField("Name", value);
        }

        public bool DoNotSync__c
        {
            get => GetField<bool>("DoNotSync__c");
            set => SetField("DoNotSync__c", value);
        }

        public string? SyncPriority__c
        {
            get => GetField<string>("SyncPriority__c");
            set => SetField("SyncPriority__c", value);
        }

        public string? BillingStreet1__c
        {
            get => GetField<string>("BillingStreet1__c");
            set => SetField("BillingStreet1__c", value);
        }

        public string? BillingStreet2__c
        {
            get => GetField<string>("BillingStreet2__c");
            set => SetField("BillingStreet2__c", value);
        }
    }

    public class ProductEvent : SalesforceObject
    {
        public ProductEvent()
        {
            ObjectType = "Product2";
        }

        public string Name
        {
            get => GetField<string>("Name") ?? string.Empty;
            set => SetField("Name", value);
        }

        public string? ProductCode
        {
            get => GetField<string>("ProductCode");
            set => SetField("ProductCode", value);
        }

        public bool IsActive
        {
            get => GetField<bool>("IsActive");
            set => SetField("IsActive", value);
        }

        public string? SyncPriority__c
        {
            get => GetField<string>("SyncPriority__c");
            set => SetField("SyncPriority__c", value);
        }

        public bool DoNotSync__c
        {
            get => GetField<bool>("DoNotSync__c");
            set => SetField("DoNotSync__c", value);
        }
    }

    public class OpportunityEvent : SalesforceObject
    {
        public OpportunityEvent()
        {
            ObjectType = "Opportunity";
        }

        public string Name
        {
            get => GetField<string>("Name") ?? string.Empty;
            set => SetField("Name", value);
        }

        public string? Salesforce_OpportunityID__c
        {
            get => GetField<string>("Salesforce_OpportunityID__c");
            set => SetField("Salesforce_OpportunityID__c", value);
        }

        public string? Artemis_ConfirmID__c
        {
            get => GetField<string>("Artemis_ConfirmID__c");
            set => SetField("Artemis_ConfirmID__c", value);
        }

        public string? StageName
        {
            get => GetField<string>("StageName");
            set => SetField("StageName", value);
        }

        public decimal Amount
        {
            get => GetField<decimal>("Amount");
            set => SetField("Amount", value);
        }
    }
}
