using Microsoft.Extensions.Logging;

namespace SalesforceExpApi.Logging
{
    /// <summary>
    /// MuleSoft-style structured logging implementation
    /// Mimics the JSON logging format used in MuleSoft flows
    /// </summary>
    public interface IMuleSoftLogger
    {
        void LogFlowStart(string flowName, string correlationId, string? businessKey = null);
        void LogFlowEnd(string flowName, string correlationId, string? businessKey = null);
        void LogOutboundRequest(string flowName, string correlationId, string endpoint, string? businessKey = null);
        void LogOutboundRequestPayload(string flowName, string correlationId, string endpoint, object payload, string? businessKey = null);
        void LogOutboundResponse(string flowName, string correlationId, string endpoint, string? businessKey = null);
        void LogOutboundResponsePayload(string flowName, string correlationId, string endpoint, object response, string? businessKey = null);
        void LogCustomMessage(LogLevel level, string message, string flowName, string correlationId, object? additionalData = null);
        void LogSyncStatus(string flowName, string correlationId, string status, string reason, string? businessKey = null);
    }
}
