using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SalesforceExpApi.Logging
{
    public class MuleSoftLogger : IMuleSoftLogger
    {
        private readonly ILogger<MuleSoftLogger> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public MuleSoftLogger(ILogger<MuleSoftLogger> logger)
        {
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
        }

        public void LogFlowStart(string flowName, string correlationId, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Flow Started",
                FlowName = flowName,
                CorrelationID = correlationId,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogFlowEnd(string flowName, string correlationId, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Flow Ended",
                FlowName = flowName,
                CorrelationID = correlationId,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundRequest(string flowName, string correlationId, string endpoint, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Request",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundRequestPayload(string flowName, string correlationId, string endpoint, object payload, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Request Payload",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BackendRequest = payload,
                BusinessKey = businessKey
            };

            _logger.LogDebug("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundResponse(string flowName, string correlationId, string endpoint, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Response",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundResponsePayload(string flowName, string correlationId, string endpoint, object response, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Response Payload",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BackendResponse = response,
                BusinessKey = businessKey
            };

            _logger.LogDebug("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogCustomMessage(LogLevel level, string message, string flowName, string correlationId, object? additionalData = null)
        {
            var logData = new
            {
                Message = message,
                FlowName = flowName,
                CorrelationID = correlationId,
                AdditionalData = additionalData
            };

            _logger.Log(level, "{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogSyncStatus(string flowName, string correlationId, string status, string reason, string? businessKey = null)
        {
            var logData = new
            {
                Message = $"Record sync status: {status}",
                FlowName = flowName,
                CorrelationID = correlationId,
                Status = status,
                Reason = reason,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }
    }
}
