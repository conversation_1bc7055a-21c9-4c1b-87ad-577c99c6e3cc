using Microsoft.AspNetCore.Http;

namespace SalesforceExpApi.Logging
{
    public class CorrelationService : ICorrelationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CorrelationService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetCorrelationId()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("CorrelationId", out var correlationId) == true)
            {
                return correlationId?.ToString() ?? GenerateNewCorrelationId();
            }
            return GenerateNewCorrelationId();
        }

        public string GetTransactionId()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("TransactionId", out var transactionId) == true)
            {
                return transactionId?.ToString() ?? GetCorrelationId();
            }
            return GetCorrelationId();
        }

        public void SetCorrelationId(string correlationId)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                context.Items["CorrelationId"] = correlationId;
            }
        }

        public void SetTransactionId(string transactionId)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                context.Items["TransactionId"] = transactionId;
            }
        }

        private static string GenerateNewCorrelationId()
        {
            return Guid.NewGuid().ToString();
        }
    }
}
