using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SalesforceExpApi.Logging.Extensions
{
    /// <summary>
    /// Extension methods for easier logging
    /// </summary>
    public static class LoggingExtensions
    {
        public static IServiceCollection AddMuleSoftLogging(this IServiceCollection services)
        {
            services.AddScoped<IMuleSoftLogger, MuleSoftLogger>();
            services.AddScoped<ICorrelationService, CorrelationService>();
            return services;
        }

        public static ILoggingBuilder AddMuleSoftJsonLogging(this ILoggingBuilder builder)
        {
            builder.AddJsonConsole(options =>
            {
                options.IncludeScopes = true;
                options.TimestampFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";
                options.JsonWriterOptions = new JsonWriterOptions
                {
                    Indented = false
                };
            });

            return builder;
        }
    }
}
