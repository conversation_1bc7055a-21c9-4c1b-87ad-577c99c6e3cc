using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace SalesforceExpApi.Logging
{
    /// <summary>
    /// Middleware to handle correlation ID extraction and injection
    /// </summary>
    public class CorrelationIdMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<CorrelationIdMiddleware> _logger;

        public CorrelationIdMiddleware(RequestDelegate next, ILogger<CorrelationIdMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Extract correlation ID from headers or generate new one
            var correlationId = context.Request.Headers["correlationId"].FirstOrDefault() 
                ?? context.Request.Headers["x-correlation-id"].FirstOrDefault()
                ?? Guid.NewGuid().ToString();

            var transactionId = context.Request.Headers["x-transactionId"].FirstOrDefault() 
                ?? correlationId;

            // Store in context
            context.Items["CorrelationId"] = correlationId;
            context.Items["TransactionId"] = transactionId;

            // Add to response headers
            context.Response.Headers.Add("x-correlation-id", correlationId);
            context.Response.Headers.Add("x-transaction-id", transactionId);

            // Add to logging scope
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["CorrelationId"] = correlationId,
                ["TransactionId"] = transactionId
            }))
            {
                await _next(context);
            }
        }
    }
}
