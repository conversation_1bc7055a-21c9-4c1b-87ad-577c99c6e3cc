# PowerShell build script for SalesforceExpApi

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "./publish",
    [switch]$SkipTests,
    [switch]$SkipRestore,
    [switch]$Clean
)

Write-Host "Building SalesforceExpApi..." -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Stop"

try {
    # Clean if requested
    if ($Clean) {
        Write-Host "Cleaning solution..." -ForegroundColor Yellow
        dotnet clean SalesforceExpApi.sln --configuration $Configuration
        if (Test-Path $OutputPath) {
            Remove-Item $OutputPath -Recurse -Force
        }
    }

    # Restore packages
    if (-not $SkipRestore) {
        Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
        dotnet restore SalesforceExpApi.sln
    }

    # Build solution
    Write-Host "Building solution..." -ForegroundColor Yellow
    dotnet build SalesforceExpApi.sln --configuration $Configuration --no-restore

    # Run tests
    if (-not $SkipTests) {
        Write-Host "Running tests..." -ForegroundColor Yellow
        dotnet test SalesforceExpApi.sln --configuration $Configuration --no-build --verbosity normal
    }

    # Publish API project
    Write-Host "Publishing API project..." -ForegroundColor Yellow
    dotnet publish src/SalesforceExpApi.Api/SalesforceExpApi.Api.csproj `
        --configuration $Configuration `
        --output $OutputPath `
        --no-build `
        --verbosity normal

    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "Published to: $OutputPath" -ForegroundColor Cyan

} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
