using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SalesforceExpApi.Logging
{
    /// <summary>
    /// MuleSoft-style structured logging implementation
    /// Mimics the JSON logging format used in MuleSoft flows
    /// </summary>
    public interface IMuleSoftLogger
    {
        void LogFlowStart(string flowName, string correlationId, string? businessKey = null);
        void LogFlowEnd(string flowName, string correlationId, string? businessKey = null);
        void LogOutboundRequest(string flowName, string correlationId, string endpoint, string? businessKey = null);
        void LogOutboundRequestPayload(string flowName, string correlationId, string endpoint, object payload, string? businessKey = null);
        void LogOutboundResponse(string flowName, string correlationId, string endpoint, string? businessKey = null);
        void LogOutboundResponsePayload(string flowName, string correlationId, string endpoint, object response, string? businessKey = null);
        void LogCustomMessage(LogLevel level, string message, string flowName, string correlationId, object? additionalData = null);
        void LogSyncStatus(string flowName, string correlationId, string status, string reason, string? businessKey = null);
    }

    public class MuleSoftLogger : IMuleSoftLogger
    {
        private readonly ILogger<MuleSoftLogger> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public MuleSoftLogger(ILogger<MuleSoftLogger> logger)
        {
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
        }

        public void LogFlowStart(string flowName, string correlationId, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Flow Started",
                FlowName = flowName,
                CorrelationID = correlationId,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogFlowEnd(string flowName, string correlationId, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Flow Ended",
                FlowName = flowName,
                CorrelationID = correlationId,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundRequest(string flowName, string correlationId, string endpoint, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Request",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundRequestPayload(string flowName, string correlationId, string endpoint, object payload, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Request Payload",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BackendRequest = payload,
                BusinessKey = businessKey
            };

            _logger.LogDebug("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundResponse(string flowName, string correlationId, string endpoint, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Response",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogOutboundResponsePayload(string flowName, string correlationId, string endpoint, object response, string? businessKey = null)
        {
            var logData = new
            {
                Message = "Log Outbound Response Payload",
                FlowName = flowName,
                CorrelationID = correlationId,
                Endpoint = endpoint,
                BackendResponse = response,
                BusinessKey = businessKey
            };

            _logger.LogDebug("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogCustomMessage(LogLevel level, string message, string flowName, string correlationId, object? additionalData = null)
        {
            var logData = new
            {
                Message = message,
                FlowName = flowName,
                CorrelationID = correlationId,
                AdditionalData = additionalData
            };

            _logger.Log(level, "{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }

        public void LogSyncStatus(string flowName, string correlationId, string status, string reason, string? businessKey = null)
        {
            var logData = new
            {
                Message = $"Record sync status: {status}",
                FlowName = flowName,
                CorrelationID = correlationId,
                Status = status,
                Reason = reason,
                BusinessKey = businessKey
            };

            _logger.LogInformation("{LogData}", JsonSerializer.Serialize(logData, _jsonOptions));
        }
    }

    /// <summary>
    /// Extension methods for easier logging
    /// </summary>
    public static class MuleSoftLoggerExtensions
    {
        public static IServiceCollection AddMuleSoftLogging(this IServiceCollection services)
        {
            services.AddScoped<IMuleSoftLogger, MuleSoftLogger>();
            return services;
        }

        public static ILoggingBuilder AddMuleSoftJsonLogging(this ILoggingBuilder builder)
        {
            builder.AddJsonConsole(options =>
            {
                options.IncludeScopes = true;
                options.TimestampFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";
                options.JsonWriterOptions = new JsonWriterOptions
                {
                    Indented = false
                };
            });

            return builder;
        }
    }

    /// <summary>
    /// Correlation ID service for tracking requests
    /// </summary>
    public interface ICorrelationService
    {
        string GetCorrelationId();
        string GetTransactionId();
        void SetCorrelationId(string correlationId);
        void SetTransactionId(string transactionId);
    }

    public class CorrelationService : ICorrelationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CorrelationService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetCorrelationId()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("CorrelationId", out var correlationId) == true)
            {
                return correlationId?.ToString() ?? GenerateNewCorrelationId();
            }
            return GenerateNewCorrelationId();
        }

        public string GetTransactionId()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("TransactionId", out var transactionId) == true)
            {
                return transactionId?.ToString() ?? GetCorrelationId();
            }
            return GetCorrelationId();
        }

        public void SetCorrelationId(string correlationId)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                context.Items["CorrelationId"] = correlationId;
            }
        }

        public void SetTransactionId(string transactionId)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                context.Items["TransactionId"] = transactionId;
            }
        }

        private static string GenerateNewCorrelationId()
        {
            return Guid.NewGuid().ToString();
        }
    }

    /// <summary>
    /// Middleware to handle correlation ID extraction and injection
    /// </summary>
    public class CorrelationIdMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<CorrelationIdMiddleware> _logger;

        public CorrelationIdMiddleware(RequestDelegate next, ILogger<CorrelationIdMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Extract correlation ID from headers or generate new one
            var correlationId = context.Request.Headers["correlationId"].FirstOrDefault() 
                ?? context.Request.Headers["x-correlation-id"].FirstOrDefault()
                ?? Guid.NewGuid().ToString();

            var transactionId = context.Request.Headers["x-transactionId"].FirstOrDefault() 
                ?? correlationId;

            // Store in context
            context.Items["CorrelationId"] = correlationId;
            context.Items["TransactionId"] = transactionId;

            // Add to response headers
            context.Response.Headers.Add("x-correlation-id", correlationId);
            context.Response.Headers.Add("x-transaction-id", transactionId);

            // Add to logging scope
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["CorrelationId"] = correlationId,
                ["TransactionId"] = transactionId
            }))
            {
                await _next(context);
            }
        }
    }

    /// <summary>
    /// Action filter for correlation ID handling
    /// </summary>
    public class CorrelationIdFilter : ActionFilterAttribute
    {
        private readonly ICorrelationService _correlationService;

        public CorrelationIdFilter(ICorrelationService correlationService)
        {
            _correlationService = correlationService;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            // Correlation ID is already set by middleware
            // This filter can be used for additional correlation logic if needed
            base.OnActionExecuting(context);
        }
    }
}
