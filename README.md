# Apisero_Salesforce_EXP_API
---

## Branching strategy

- development: Account, Order, Invoice sync
- development-U2.1: Product sync
- development-U3.2: Confirm sync
---

## Deployment strategy

- salesforce-sys-api-dev: Order, Invoice, Confirm sync
- salesforce-sys-api-2-1-dev: Account, Product sync
---

## INTERFACE

[interface](documentation/interface.md) API Router & Endpoints

## COMMON

[global](documentation/global.md) Global Configuration Elements  
[global-error-handler](documentation/global-error-handler.md) Global Error Handler  

## IMPLEMENTATION

[pf-notify-sf-account-delete](documentation/pf-notify-sf-account-delete.md) Insert Queued Salesforce Account Record for Deletion in `Enterprise_ID`  
[pf-on-sf-account-created](documentation/pf-on-sf-account-created.md) Insert Queued Salesforce Account Record for Creation in `Enterprise_ID`  
[pf-on-sf-account-modified](documentation/pf-on-sf-account-modified.md) Insert Queued Salesforce Account Record for Modification in `Enterprise_ID`  

---

## CONFIGURATION

[config](documentation/config.md) Configuration
