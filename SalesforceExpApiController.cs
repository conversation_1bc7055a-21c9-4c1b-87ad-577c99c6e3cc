using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using SalesforceExpApi.Models;
using SalesforceExpApi.Services;
using SalesforceExpApi.Attributes;

namespace SalesforceExpApi.Controllers
{
    [ApiController]
    [Route("api")]
    [Authorize(Policy = "ClientIdEnforcement")]
    [ServiceFilter(typeof(CorrelationIdFilter))]
    public class SalesforceExpApiController : ControllerBase
    {
        private readonly ILogger<SalesforceExpApiController> _logger;
        private readonly IOrderProcessingService _orderService;
        private readonly IInvoiceProcessingService _invoiceService;
        private readonly IAccountDeletionService _accountDeletionService;
        private readonly ICorrelationService _correlationService;

        public SalesforceExpApiController(
            ILogger<SalesforceExpApiController> logger,
            IOrderProcessingService orderService,
            IInvoiceProcessingService invoiceService,
            IAccountDeletionService accountDeletionService,
            ICorrelationService correlationService)
        {
            _logger = logger;
            _orderService = orderService;
            _invoiceService = invoiceService;
            _accountDeletionService = accountDeletionService;
            _correlationService = correlationService;
        }

        /// <summary>
        /// Create Order - Equivalent to post:\orders:application\json
        /// </summary>
        [HttpPost("orders")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(CreateOrderResponse), 201)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> CreateOrder([FromBody] CreateOrderRequest request)
        {
            var correlationId = _correlationService.GetCorrelationId();
            var transactionId = _correlationService.GetTransactionId();
            var businessKey = $"SalesOrderID-{request.Order.SfId}";

            _logger.LogInformation("Flow Started: pf-on-sf-order-create, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                correlationId, businessKey);

            try
            {
                var result = await _orderService.CreateOrderAsync(request, correlationId, transactionId, businessKey);
                
                _logger.LogInformation("Flow Ended: pf-on-sf-order-create, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    correlationId, businessKey);

                return StatusCode(201, result);
            }
            catch (DataValidationException ex)
            {
                _logger.LogError(ex, "Data validation error in CreateOrder, CorrelationID: {CorrelationId}", correlationId);
                return BadRequest(new ErrorResponse 
                { 
                    Code = 400, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "DATA_VALIDATION_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Internal server error in CreateOrder, CorrelationID: {CorrelationId}", correlationId);
                return StatusCode(500, new ErrorResponse 
                { 
                    Code = 500, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "INTERNAL_SERVER_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
        }

        /// <summary>
        /// Update Order - Equivalent to put:\orders:application\json
        /// </summary>
        [HttpPut("orders")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(UpdateOrderResponse), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> UpdateOrder([FromBody] UpdateOrderRequest request)
        {
            var correlationId = _correlationService.GetCorrelationId();
            var transactionId = _correlationService.GetTransactionId();
            var businessKey = $"SalesOrderID-{request.Order.SfId}";

            _logger.LogInformation("Flow Started: pf-on-sf-order-update, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                correlationId, businessKey);

            try
            {
                var result = await _orderService.UpdateOrderAsync(request, correlationId, transactionId, businessKey);
                
                _logger.LogInformation("Flow Ended: pf-on-sf-order-update, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    correlationId, businessKey);

                return Ok(result);
            }
            catch (DataValidationException ex)
            {
                _logger.LogError(ex, "Data validation error in UpdateOrder, CorrelationID: {CorrelationId}", correlationId);
                return BadRequest(new ErrorResponse 
                { 
                    Code = 400, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "DATA_VALIDATION_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Internal server error in UpdateOrder, CorrelationID: {CorrelationId}", correlationId);
                return StatusCode(500, new ErrorResponse 
                { 
                    Code = 500, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "INTERNAL_SERVER_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
        }

        /// <summary>
        /// Delete Order - Equivalent to delete:\orders
        /// </summary>
        [HttpDelete("orders")]
        [ProducesResponseType(typeof(DeleteOrderResponse), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> DeleteOrder([FromQuery, Required] string orderNetsuiteId)
        {
            var correlationId = _correlationService.GetCorrelationId();
            var transactionId = _correlationService.GetTransactionId();
            var businessKey = $"SalesOrderID-{orderNetsuiteId}";

            _logger.LogInformation("Flow Started: pf-on-sf-order-delete, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                correlationId, businessKey);

            try
            {
                var result = await _orderService.DeleteOrderAsync(orderNetsuiteId, correlationId, transactionId, businessKey);
                
                _logger.LogInformation("Flow Ended: pf-on-sf-order-delete, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    correlationId, businessKey);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Internal server error in DeleteOrder, CorrelationID: {CorrelationId}", correlationId);
                return StatusCode(500, new ErrorResponse 
                { 
                    Code = 500, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "INTERNAL_SERVER_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
        }

        /// <summary>
        /// Update Invoice - Equivalent to put:\invoices:application\json
        /// </summary>
        [HttpPut("invoices")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(UpdateInvoiceResponse), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> UpdateInvoice([FromBody] UpdateInvoiceRequest request)
        {
            var correlationId = _correlationService.GetCorrelationId();
            var transactionId = _correlationService.GetTransactionId();
            var businessKey = $"InvoiceID-{request.Invoice.InvoiceNSId}";

            _logger.LogInformation("Flow Started: pf-on-sf-invoice-updated, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                correlationId, businessKey);

            try
            {
                var result = await _invoiceService.UpdateInvoiceAsync(request, correlationId, transactionId, businessKey);
                
                _logger.LogInformation("Flow Ended: pf-on-sf-invoice-updated, CorrelationID: {CorrelationId}, BusinessKey: {BusinessKey}", 
                    correlationId, businessKey);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Internal server error in UpdateInvoice, CorrelationID: {CorrelationId}", correlationId);
                return StatusCode(500, new ErrorResponse 
                { 
                    Code = 500, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "INTERNAL_SERVER_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
        }

        /// <summary>
        /// Notify Account Delete - Equivalent to get:\notifyDelete
        /// </summary>
        [HttpGet("notifyDelete")]
        [ProducesResponseType(typeof(NotifyDeleteResponse), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> NotifyDelete(
            [FromQuery, Required] string id,
            [FromQuery, Required] string objectType,
            [FromQuery, Required] string updatedBy,
            [FromQuery] int? syncPriority = null)
        {
            var correlationId = _correlationService.GetCorrelationId();

            _logger.LogInformation("Flow Started: pf-notify-sf-account-delete, CorrelationID: {CorrelationId}", correlationId);

            try
            {
                var result = await _accountDeletionService.ProcessAccountDeletionAsync(
                    id, objectType, updatedBy, syncPriority, correlationId);
                
                _logger.LogInformation("Flow Ended: pf-notify-sf-account-delete, CorrelationID: {CorrelationId}", correlationId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Internal server error in NotifyDelete, CorrelationID: {CorrelationId}", correlationId);
                return StatusCode(500, new ErrorResponse 
                { 
                    Code = 500, 
                    Status = "FAILURE", 
                    TransactionId = correlationId,
                    Response = new ErrorDetails 
                    { 
                        Message = "INTERNAL_SERVER_ERROR", 
                        Details = ex.Message 
                    }
                });
            }
        }
    }
}
