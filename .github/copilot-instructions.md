Implement a C# controller to handle this MuleSoft HTTP listener configuration. Provide details of the MuleSoft HTTP Request (URL, headers, body, authentication, etc.). 

Describe the MuleSoft error handling logic (catch specific errors, log messages, etc.) 

Generate a C# logging mechanism similar to the MuleSoft Logger component, using a common logging framework

Suggest a C# project structure for migrating this MuleSoft application, considering its modularity and dependencies

Identify and list the key challenges and considerations when migrating this MuleSoft DataWeave script to C#