Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docker", "docker", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Api", "src\SalesforceExpApi.Api\SalesforceExpApi.Api.csproj", "{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Core", "src\SalesforceExpApi.Core\SalesforceExpApi.Core.csproj", "{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Infrastructure", "src\SalesforceExpApi.Infrastructure\SalesforceExpApi.Infrastructure.csproj", "{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Logging", "src\SalesforceExpApi.Logging\SalesforceExpApi.Logging.csproj", "{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.EventProcessing", "src\SalesforceExpApi.EventProcessing\SalesforceExpApi.EventProcessing.csproj", "{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Api.Tests", "tests\SalesforceExpApi.Api.Tests\SalesforceExpApi.Api.Tests.csproj", "{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Core.Tests", "tests\SalesforceExpApi.Core.Tests\SalesforceExpApi.Core.Tests.csproj", "{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.Infrastructure.Tests", "tests\SalesforceExpApi.Infrastructure.Tests\SalesforceExpApi.Infrastructure.Tests.csproj", "{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SalesforceExpApi.EventProcessing.Tests", "tests\SalesforceExpApi.EventProcessing.Tests\SalesforceExpApi.EventProcessing.Tests.csproj", "{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A2FE74E2-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A2FE74E3-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A2FE74E4-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{A2FE74E5-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{B2FE74E1-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
		{B2FE74E2-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
		{B2FE74E3-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
		{B2FE74E4-B743-11D0-AE1A-00A0C90FFFC3} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}
	EndGlobalSection
EndGlobal
