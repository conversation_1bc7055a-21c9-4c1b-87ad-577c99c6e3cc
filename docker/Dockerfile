# Use the official .NET 8 runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8443

# Use the SDK image to build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["src/SalesforceExpApi.Api/SalesforceExpApi.Api.csproj", "src/SalesforceExpApi.Api/"]
COPY ["src/SalesforceExpApi.Core/SalesforceExpApi.Core.csproj", "src/SalesforceExpApi.Core/"]
COPY ["src/SalesforceExpApi.Infrastructure/SalesforceExpApi.Infrastructure.csproj", "src/SalesforceExpApi.Infrastructure/"]
COPY ["src/SalesforceExpApi.Logging/SalesforceExpApi.Logging.csproj", "src/SalesforceExpApi.Logging/"]
COPY ["src/SalesforceExpApi.EventProcessing/SalesforceExpApi.EventProcessing.csproj", "src/SalesforceExpApi.EventProcessing/"]

RUN dotnet restore "src/SalesforceExpApi.Api/SalesforceExpApi.Api.csproj"

# Copy the rest of the application code
COPY . .

# Build the application
WORKDIR "/src/src/SalesforceExpApi.Api"
RUN dotnet build "SalesforceExpApi.Api.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "SalesforceExpApi.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

COPY --from=publish /app/publish .

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["dotnet", "SalesforceExpApi.Api.dll"]
