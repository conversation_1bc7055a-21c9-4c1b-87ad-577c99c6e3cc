version: '3.8'

services:
  salesforce-exp-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: salesforce-exp-api
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080;https://+:8443
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    volumes:
      - ./certificates:/https:ro
      - ./logs:/app/logs
    networks:
      - salesforce-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy (nginx)
  nginx:
    image: nginx:alpine
    container_name: salesforce-exp-api-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certificates:/etc/nginx/certs:ro
    depends_on:
      - salesforce-exp-api
    networks:
      - salesforce-network
    restart: unless-stopped

networks:
  salesforce-network:
    driver: bridge

volumes:
  app-logs:
    driver: local
