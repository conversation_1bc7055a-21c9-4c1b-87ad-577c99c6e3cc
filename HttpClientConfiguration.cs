using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using <PERSON>;
using Polly.Extensions.Http;

namespace SalesforceExpApi.Configuration
{
    public class HttpClientConfiguration
    {
        public class TransactionDbApiConfig
        {
            public string Host { get; set; } = string.Empty;
            public int Port { get; set; } = 443;
            public int ConnectionTimeout { get; set; } = 30000;
            public int ResponseTimeout { get; set; } = 30000;
            public int ReconnectionFrequency { get; set; } = 1000;
            public int ReconnectionAttempts { get; set; } = 3;
            public string TruststorePath { get; set; } = string.Empty;
            public string TruststorePassword { get; set; } = string.Empty;
            
            // API Endpoints
            public string RefIdPath { get; set; } = "/api/REF_ID";
            public string TransactionDetailsPath { get; set; } = "/api/TRANSACTION";
            public string TransactionTaskDetailsPath { get; set; } = "/api/TRANSACTION_TASK";
        }

        public class SyncPrcApiConfig
        {
            public string Host { get; set; } = string.Empty;
            public int Port { get; set; } = 443;
            public string BasePath { get; set; } = "/";
            public int ConnectionTimeout { get; set; } = 30000;
            public int ReconnectionFrequency { get; set; } = 1000;
            public int ReconnectionAttempts { get; set; } = 3;
            public string ClientId { get; set; } = string.Empty;
            public string ClientSecret { get; set; } = string.Empty;
            public string TruststorePath { get; set; } = string.Empty;
            public string TruststorePassword { get; set; } = string.Empty;
            
            // API Endpoints
            public string SyncRecordsPath { get; set; } = "/api/syncRecords";
            public string SyncProductsPath { get; set; } = "/api/syncProducts";
        }

        public class OrderPrcApiConfig
        {
            public string Host { get; set; } = string.Empty;
            public int Port { get; set; } = 443;
            public string BasePath { get; set; } = "/";
            public int ConnectionTimeout { get; set; } = 30000;
            public int ResponseTimeout { get; set; } = 185000;
            public int ReconnectionFrequency { get; set; } = 1000;
            public int ReconnectionAttempts { get; set; } = 3;
            public string ClientId { get; set; } = string.Empty;
            public string ClientSecret { get; set; } = string.Empty;
            public string TruststorePath { get; set; } = string.Empty;
            public string TruststorePassword { get; set; } = string.Empty;
            
            // API Endpoints
            public string OrdersPath { get; set; } = "/api/orders";
            public string InvoicesPath { get; set; } = "/api/invoices";
            public string OpportunitiesPath { get; set; } = "/api/opportunities";
        }

        public TransactionDbApiConfig TransactionDbApi { get; set; } = new();
        public SyncPrcApiConfig SyncPrcApi { get; set; } = new();
        public OrderPrcApiConfig OrderPrcApi { get; set; } = new();
    }

    public static class HttpClientConfigurationExtensions
    {
        public static IServiceCollection AddHttpClientConfigurations(
            this IServiceCollection services, 
            IConfiguration configuration)
        {
            var httpConfig = configuration.GetSection("HttpClients").Get<HttpClientConfiguration>() 
                ?? new HttpClientConfiguration();

            // Configure Transaction DB API HttpClient
            services.AddHttpClient("TransactionDbApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.TransactionDbApi.Host}:{httpConfig.TransactionDbApi.Port}");
                client.Timeout = TimeSpan.FromMilliseconds(httpConfig.TransactionDbApi.ResponseTimeout);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
                client.DefaultRequestHeaders.Add("x-source", "SALESFORCE");
                client.DefaultRequestHeaders.Add("sourceId", "SALESFORCE_EXP_API");
                client.DefaultRequestHeaders.Add("destinationId", "TRANSACTION_DB_SYS_API");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(httpConfig.TransactionDbApi.TruststorePath, httpConfig.TransactionDbApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(httpConfig.TransactionDbApi.ReconnectionAttempts, httpConfig.TransactionDbApi.ReconnectionFrequency));

            // Configure Sync Process API HttpClient
            services.AddHttpClient("SyncPrcApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.SyncPrcApi.Host}:{httpConfig.SyncPrcApi.Port}{httpConfig.SyncPrcApi.BasePath}");
                client.Timeout = TimeSpan.FromMilliseconds(30000); // Default timeout
                client.DefaultRequestHeaders.Add("client_id", httpConfig.SyncPrcApi.ClientId);
                client.DefaultRequestHeaders.Add("client_secret", httpConfig.SyncPrcApi.ClientSecret);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(httpConfig.SyncPrcApi.TruststorePath, httpConfig.SyncPrcApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(httpConfig.SyncPrcApi.ReconnectionAttempts, httpConfig.SyncPrcApi.ReconnectionFrequency));

            // Configure Order Process API HttpClient
            services.AddHttpClient("OrderPrcApi", client =>
            {
                client.BaseAddress = new Uri($"https://{httpConfig.OrderPrcApi.Host}:{httpConfig.OrderPrcApi.Port}{httpConfig.OrderPrcApi.BasePath}");
                client.Timeout = TimeSpan.FromMilliseconds(httpConfig.OrderPrcApi.ResponseTimeout);
                client.DefaultRequestHeaders.Add("client_id", httpConfig.OrderPrcApi.ClientId);
                client.DefaultRequestHeaders.Add("client_secret", httpConfig.OrderPrcApi.ClientSecret);
                client.DefaultRequestHeaders.Add("Content-Type", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => CreateHttpMessageHandler(httpConfig.OrderPrcApi.TruststorePath, httpConfig.OrderPrcApi.TruststorePassword))
            .AddPolicyHandler(GetRetryPolicy(httpConfig.OrderPrcApi.ReconnectionAttempts, httpConfig.OrderPrcApi.ReconnectionFrequency));

            return services;
        }

        private static HttpMessageHandler CreateHttpMessageHandler(string truststorePath, string truststorePassword)
        {
            var handler = new HttpClientHandler();
            
            if (!string.IsNullOrEmpty(truststorePath))
            {
                try
                {
                    // Load certificate from truststore
                    var certificate = new X509Certificate2(truststorePath, truststorePassword);
                    handler.ClientCertificates.Add(certificate);
                    
                    // Configure SSL validation
                    handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
                    {
                        // Custom certificate validation logic
                        if (sslPolicyErrors == SslPolicyErrors.None)
                            return true;

                        // Log certificate validation issues
                        Console.WriteLine($"SSL Policy Errors: {sslPolicyErrors}");
                        
                        // For development/testing, you might want to be more permissive
                        // In production, implement proper certificate validation
                        return sslPolicyErrors == SslPolicyErrors.RemoteCertificateNameMismatch ||
                               sslPolicyErrors == SslPolicyErrors.RemoteCertificateChainErrors;
                    };
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to load certificate from {truststorePath}: {ex.Message}");
                    // Continue without client certificate
                }
            }

            return handler;
        }

        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int maxRetries, int delayMs)
        {
            return Policy
                .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
                .Or<HttpRequestException>()
                .Or<TaskCanceledException>()
                .WaitAndRetryAsync(
                    maxRetries,
                    retryAttempt => TimeSpan.FromMilliseconds(delayMs * retryAttempt),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        Console.WriteLine($"Retry {retryCount} after {timespan} seconds");
                    });
        }
    }

    // HTTP Request Headers Builder
    public class HttpRequestHeadersBuilder
    {
        private readonly Dictionary<string, string> _headers = new();

        public HttpRequestHeadersBuilder WithCorrelationId(string correlationId)
        {
            _headers["correlationId"] = correlationId;
            return this;
        }

        public HttpRequestHeadersBuilder WithTransactionId(string transactionId)
        {
            _headers["x-transactionId"] = transactionId;
            return this;
        }

        public HttpRequestHeadersBuilder WithSource(string source)
        {
            _headers["x-source"] = source;
            return this;
        }

        public HttpRequestHeadersBuilder WithSourceId(string sourceId)
        {
            _headers["sourceId"] = sourceId;
            return this;
        }

        public HttpRequestHeadersBuilder WithDestinationId(string destinationId)
        {
            _headers["destinationId"] = destinationId;
            return this;
        }

        public HttpRequestHeadersBuilder WithBusinessKey(string businessKey)
        {
            _headers["x-businessKey"] = businessKey;
            return this;
        }

        public HttpRequestHeadersBuilder WithTimestamp(DateTime timestamp)
        {
            _headers["x-msg-timestamp"] = timestamp.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            return this;
        }

        public HttpRequestHeadersBuilder WithCustomHeader(string key, string value)
        {
            _headers[key] = value;
            return this;
        }

        public Dictionary<string, string> Build() => new(_headers);
    }
}
