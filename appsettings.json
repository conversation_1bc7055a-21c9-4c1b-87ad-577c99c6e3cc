{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "SalesforceExpApi": "Debug"}, "Console": {"FormatterName": "json", "FormatterOptions": {"SingleLine": false, "IncludeScopes": true, "TimestampFormat": "yyyy-MM-ddTHH:mm:ss.fffZ", "UseUtcTimestamp": true}}}, "AllowedHosts": "*", "HttpClients": {"TransactionDbApi": {"Host": "localhost", "Port": 443, "ConnectionTimeout": 30000, "ResponseTimeout": 30000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "TruststorePath": "certificates/truststore.pfx", "TruststorePassword": "password", "RefIdPath": "/api/REF_ID", "TransactionDetailsPath": "/api/TRANSACTION", "TransactionTaskDetailsPath": "/api/TRANSACTION_TASK"}, "SyncPrcApi": {"Host": "localhost", "Port": 443, "BasePath": "/", "ConnectionTimeout": 30000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "ClientId": "your-client-id", "ClientSecret": "your-client-secret", "TruststorePath": "certificates/truststore.pfx", "TruststorePassword": "password", "SyncRecordsPath": "/api/syncRecords", "SyncProductsPath": "/api/syncProducts"}, "OrderPrcApi": {"Host": "localhost", "Port": 443, "BasePath": "/", "ConnectionTimeout": 30000, "ResponseTimeout": 185000, "ReconnectionFrequency": 1000, "ReconnectionAttempts": 3, "ClientId": "your-client-id", "ClientSecret": "your-client-secret", "TruststorePath": "certificates/truststore.pfx", "TruststorePassword": "password", "OrdersPath": "/api/orders", "InvoicesPath": "/api/invoices", "OpportunitiesPath": "/api/opportunities"}}, "Salesforce": {"ConsumerKey": "your-consumer-key", "ConsumerSecret": "your-consumer-secret", "Username": "your-username", "Password": "your-password", "SecurityToken": "your-security-token", "LoginUrl": "https://login.salesforce.com", "ApiVersion": "v58.0", "User": {"MulesoftId": "your-mulesoft-user-id"}, "Poll": {"Frequency": 5000, "StartDelay": 1000}, "CreateTimeDiff": 300, "Channel": {"OpportunityChangeEvent": {"Name": "/event/Ready_For_Fulfillment__e"}}}, "FlowStates": {"AccountCreatedFlowState": "started", "AccountModifiedFlowState": "started", "ProductCreatedFlowState": "started", "ProductModifiedFlowState": "started"}, "EnableSyncForSalesforce": "1", "Authentication": {"ClientIdEnforcement": {"RequireClientId": true, "ValidClientIds": ["client-id-1", "client-id-2"]}, "Jwt": {"Issuer": "SalesforceExpApi", "Audience": "SalesforceExpApi", "SecretKey": "your-jwt-secret-key-here-make-it-long-and-secure"}}, "Https": {"Listener": {"Host": "0.0.0.0", "Port": 8443, "ReadTimeout": 30000, "IdleTimeout": 60000, "KeystorePath": "certificates/server.pfx", "KeystorePassword": "password"}}, "HealthChecks": {"Enabled": true, "Endpoints": {"TransactionDbApi": "https://localhost:443/health", "SyncPrcApi": "https://localhost:443/health", "OrderPrcApi": "https://localhost:443/health", "Salesforce": "https://login.salesforce.com"}}, "Swagger": {"Title": "Salesforce Experience API", "Description": "API for Salesforce data synchronization and processing", "Version": "v1", "ContactName": "API Support", "ContactEmail": "<EMAIL>"}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "https://your-frontend-domain.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Content-Type", "Authorization", "correlationId", "x-transactionId", "x-source", "sourceId", "destinationId", "x-businessKey"]}, "RateLimiting": {"EnableRateLimiting": true, "PermitLimit": 100, "Window": "00:01:00", "ReplenishmentPeriod": "00:00:10", "TokensPerPeriod": 10, "QueueLimit": 50}, "Caching": {"DefaultExpiration": "00:15:00", "RefIdCacheExpiration": "01:00:00", "ConfigurationCacheExpiration": "00:30:00"}, "Monitoring": {"ApplicationInsights": {"InstrumentationKey": "your-app-insights-key"}, "Metrics": {"EnableCustomMetrics": true, "MetricPrefix": "SalesforceExpApi"}}}