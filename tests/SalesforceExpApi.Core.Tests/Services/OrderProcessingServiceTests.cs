using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using SalesforceExpApi.Core.Services;
using SalesforceExpApi.Core.Models.Requests;
using SalesforceExpApi.Core.Models.Domain;
using SalesforceExpApi.Core.Exceptions;

namespace SalesforceExpApi.Core.Tests.Services
{
    public class OrderProcessingServiceTests
    {
        private readonly Mock<ILogger<OrderProcessingService>> _mockLogger;
        private readonly OrderProcessingService _service;

        public OrderProcessingServiceTests()
        {
            _mockLogger = new Mock<ILogger<OrderProcessingService>>();
            _service = new OrderProcessingService(_mockLogger.Object);
        }

        [Fact]
        public async Task CreateOrderAsync_WithValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = new CreateOrderRequest
            {
                Order = new Order
                {
                    SfId = "SF123456",
                    Name = "Test Order",
                    AccountId = "ACC123",
                    Status = "Draft",
                    TotalAmount = 1000.00m
                }
            };
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = $"SalesOrderID-{request.Order.SfId}";

            // Act
            var result = await _service.CreateOrderAsync(request, correlationId, transactionId, businessKey);

            // Assert
            result.Should().NotBeNull();
            result.Code.Should().Be(201);
            result.Status.Should().Be("SUCCESS");
            result.TransactionId.Should().Be(correlationId);
            result.Response.Should().NotBeNull();
            result.Response.Message.Should().Be("Order created successfully");
            result.Response.OrderId.Should().Be(request.Order.SfId);
        }

        [Fact]
        public async Task CreateOrderAsync_WithNullRequest_ShouldThrowDataValidationException()
        {
            // Arrange
            CreateOrderRequest? request = null;
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = "SalesOrderID-null";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<DataValidationException>(
                () => _service.CreateOrderAsync(request!, correlationId, transactionId, businessKey));

            exception.Message.Should().Be("Order data is required");
        }

        [Fact]
        public async Task CreateOrderAsync_WithNullOrder_ShouldThrowDataValidationException()
        {
            // Arrange
            var request = new CreateOrderRequest
            {
                Order = null!
            };
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = "SalesOrderID-null";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<DataValidationException>(
                () => _service.CreateOrderAsync(request, correlationId, transactionId, businessKey));

            exception.Message.Should().Be("Order data is required");
        }

        [Fact]
        public async Task CreateOrderAsync_WithEmptySfId_ShouldThrowDataValidationException()
        {
            // Arrange
            var request = new CreateOrderRequest
            {
                Order = new Order
                {
                    SfId = string.Empty,
                    Name = "Test Order",
                    AccountId = "ACC123",
                    Status = "Draft",
                    TotalAmount = 1000.00m
                }
            };
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = "SalesOrderID-empty";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<DataValidationException>(
                () => _service.CreateOrderAsync(request, correlationId, transactionId, businessKey));

            exception.Message.Should().Be("Order SfId is required");
        }

        [Fact]
        public async Task UpdateOrderAsync_WithValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = new UpdateOrderRequest
            {
                Order = new Order
                {
                    SfId = "SF123456",
                    NetsuiteId = "NS789",
                    Name = "Updated Test Order",
                    AccountId = "ACC123",
                    Status = "Active",
                    TotalAmount = 1500.00m
                }
            };
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = $"SalesOrderID-{request.Order.SfId}";

            // Act
            var result = await _service.UpdateOrderAsync(request, correlationId, transactionId, businessKey);

            // Assert
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Status.Should().Be("SUCCESS");
            result.TransactionId.Should().Be(correlationId);
            result.Response.Should().NotBeNull();
            result.Response.Message.Should().Be("Order updated successfully");
            result.Response.OrderId.Should().Be(request.Order.SfId);
            result.Response.NetsuiteId.Should().Be(request.Order.NetsuiteId);
        }

        [Fact]
        public async Task DeleteOrderAsync_WithValidNetsuiteId_ShouldReturnSuccessResponse()
        {
            // Arrange
            var orderNetsuiteId = "NS789";
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = $"SalesOrderID-{orderNetsuiteId}";

            // Act
            var result = await _service.DeleteOrderAsync(orderNetsuiteId, correlationId, transactionId, businessKey);

            // Assert
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Status.Should().Be("SUCCESS");
            result.TransactionId.Should().Be(correlationId);
            result.Response.Should().NotBeNull();
            result.Response.Message.Should().Be("Order deleted successfully");
            result.Response.NetsuiteId.Should().Be(orderNetsuiteId);
        }

        [Fact]
        public async Task DeleteOrderAsync_WithEmptyNetsuiteId_ShouldThrowDataValidationException()
        {
            // Arrange
            var orderNetsuiteId = string.Empty;
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = "SalesOrderID-empty";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<DataValidationException>(
                () => _service.DeleteOrderAsync(orderNetsuiteId, correlationId, transactionId, businessKey));

            exception.Message.Should().Be("Order NetSuite ID is required");
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData(" ")]
        public async Task DeleteOrderAsync_WithInvalidNetsuiteId_ShouldThrowDataValidationException(string? invalidNetsuiteId)
        {
            // Arrange
            var correlationId = Guid.NewGuid().ToString();
            var transactionId = correlationId;
            var businessKey = "SalesOrderID-invalid";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<DataValidationException>(
                () => _service.DeleteOrderAsync(invalidNetsuiteId!, correlationId, transactionId, businessKey));

            exception.Message.Should().Be("Order NetSuite ID is required");
        }
    }
}
