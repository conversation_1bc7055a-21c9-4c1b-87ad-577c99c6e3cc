# global

![global](documentation/global.png)

> properties used in global elements are defined in `.properties` files

## HTTPS_Listener_config

```xml
<http:listener-config name="HTTPS_Listener_config"
  doc:name="HTTP Listener config"
  doc:id="5a962383-4f3f-48ff-b535-6b6b7fa3af52">
  <http:listener-connection
   host="${https.listener.host}" port="${https.listener.port}"
   readTimeout="${https.listener.readTimeout}"
   connectionIdleTimeout="${https.listener.idleTimeout}"
   protocol="HTTPS" tlsContext="TLS_Context_Inbound">
  </http:listener-connection>
 </http:listener-config>
```

## TLS_Context_Inbound

```xml
<tls:context name="TLS_Context_Inbound"
  doc:name="TLS Context" doc:id="6f395269-8b74-47e5-afb0-ff1e9f09de72">
  <tls:key-store type="jks"
   path="${https.listener.keystore.path}"
   keyPassword="${secure::https.listener.keystore.keyPassword}"
   password="${secure::https.listener.keystore.password}" />
 </tls:context>
```

## TLS_Context_Transaction_DB_Outbound

```xml
<tls:context name="TLS_Context_Transaction_DB_Outbound"
  doc:name="TLS Context" doc:id="383a67a0-cddf-4dbf-98d0-2b64e02f8796">
  <tls:trust-store
   path="${https.request.transactionDBSysApi.truststore.path}"
   password="${secure::https.request.transactionDBSysApi.truststore.password}"
   type="jks" />
 </tls:context>
```

## APIKit

```xml
<apikit:config name="3degreesSalesforceExpAPI-config" api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:3degrees-salesforce-exp-api:1.0.2:raml:zip:3degreesSalesforceExpAPI.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus" />
```

## Configuration Properties (config-common.properties)

```xml
<configuration-properties doc:name="Configuration properties" doc:id="e954c833-48d2-4eef-868f-4ff071e91fdd" file="config\config-common.properties" />
```

## Configuration Properties (config\config-${mule.env}.properties)

```xml
<configuration-properties doc:name="Configuration properties" doc:id="70b0cb66-86bb-4882-b772-4ee9e3c47eff" file="config\config-${mule.env}.properties" /> 
 ```

## Secure_Properties_Config

```xml
<secure-properties:config name="Secure_Properties_Config" doc:name="Secure Properties Config" doc:id="c8a291a0-1465-4230-ac2e-1b1d7a688480" file="config\config-${mule.env}-secure.properties" key="${mule.key}" >
  <secure-properties:encrypt algorithm="Blowfish" />
 </secure-properties:config>
 ```

## HTTPS_Request_Transaction_DB_SYS_API

```xml  
<http:request-config name="HTTPS_Request_Transaction_DB_SYS_API" doc:name="HTTP Request configuration" doc:id="ff283648-47f5-4a1d-826b-de4f8145aef7" responseTimeout="#[p('https.request.transactionDBSysApi.responseTimeout')]">
  <http:request-connection protocol="HTTPS" host="#[p('https.request.transactionDBSysApi.host')]" port="#[p('https.request.transactionDBSysApi.port')]" connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}">
   <reconnection >
    <reconnect frequency="${https.request.transactionDBSysApi.reconnection.frequency}" count="${https.request.transactionDBSysApi.reconnection.attempts}" />
   </reconnection>
   <tls:context >
    <tls:trust-store insecure="true" />
   </tls:context>
  </http:request-connection>
  <http:default-headers >
   <http:default-header key="client_id" value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
   <http:default-header key="client_secret" value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
  </http:default-headers>
 </http:request-config>
```

## Salesforce_Config

```xml
<salesforce:sfdc-config name="Salesforce_Config" doc:name="Salesforce Config" doc:id="9daed457-300b-462f-b4b4-2ccfb71427ff" >
  <salesforce:jwt-connection consumerKey="${secure::salesforce.consumerKey}" keyStore="${salesforce.keystorePath}" storePassword="${secure::salesforce.keystorePassword}" certificateAlias="${secure::salesforce.certificateAlias}" principal="${salesforce.principal}" tokenEndpoint="${salesforce.tokenEndpoint}" audienceUrl="${salesforce.audienceUrl}" objectTTL="#[p('salesforce.connection.objectTtl')]" maxEntries="#[p('salesforce.connection.maxEntries')]"/>
 </salesforce:sfdc-config>
```

## Configuration: Global Error Handler

```xml
<configuration doc:name="Configuration" doc:id="2e6b24bb-cb27-4cd1-a6c1-05cee496f616" defaultErrorHandler-ref="global-error-handler" />
```

## API Autodiscovery

```xml
<api-gateway:autodiscovery apiId="${api.autodiscoveryId}" ignoreBasePath="true" doc:name="API Autodiscovery" doc:id="443b2dc3-b784-4742-8f36-384b3a43779e" flowRef="3degreesSalesforceExpAPI-main" />
```
