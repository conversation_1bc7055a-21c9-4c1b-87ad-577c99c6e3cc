# pf-notify-sf-account-delete

## Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

## Variable: vSalesforceId

`attributes.queryParams.'id'`

## HTTP Request: Fetch ENTERPRISE_ID from REF_ID

- Method: GET
- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Path: `p('https.request.dbSysApi.refId.path')`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Query Parameters: ```json
{
  "ARTEMIS_ID": vars.vSalesforceId,
  "OBJECT_TYPE": vars.vObjectType
}```
- Target Variable: `vRefIdResponse`

## Transform Message: Set vTransactionDetails Record

```json
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "DELETE",
    "SOURCE": "SALESFORCE",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID": vars.vRefIdResponse.response[0].'ENTERPRISE_ID', 
    "PAYLOAD": null,
    "OBJECT_TYPE": "ACCOUNT",
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
     "key": ($$),
  "val": ($)
 }) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null
  }
}
```

## HTTP Request: Insert into TRANSACTION_DETAILS

- Method: POST
- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Path: `p('https.request.dbSysApi.transactionDetails.path')`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Body: `vars.vTransactionDetailsRecord`
- Target Variable: `vInsertTransactionResponse`

## Payload

```json
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "message": "Request for delete notification has been submitted for validation and processing."
  }
}
```

## End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit
