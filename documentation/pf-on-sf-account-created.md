# pf-on-sf-account-created

## Flow: pf-on-sf-account-created

### On New Object: On Account Record Created

- Configuration: Salesforce_Config
- Object Type: Account
- Scheduling Strategy: Fixed Frequency
  - Frequency: `${salesforce.poll.frequency}`
  - Start Delay: `${salesforce.poll.startDelay}`
  - Time Unit: `MILLISECONDS`

### Transform Message: Set vSalesforcePayload

`payload`

### Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

### Choice: Check if Sync is Required

```mermaid
flowchart TD
    A{Check if Sync is Required}
    subgraph When[When vars.vSalesforcePayload.'LastModifiedById' != salesforce.user.mulesoft.id AND vars.vSalesforcePayload.'DoNotSync__c' == false]
    W1[Variable: Set vRequestAttributes]
    W2[HTTP Request: Insert into REF_ID]
    W3[Transform Message: Set vTransactionDetails Record]
    W4[HTTP Request: Insert into TRANSACTION_DETAILS]
    W5[Payload]
    W1 --> W2 --> W3 --> W4 --> W5
    end 
    A --> When
    subgraph Default
    D1[Logger: LOG INFO: Discard sync]
    end
    A --> Default
```

#### Variable: vRequestAttributes

```json
{
  "headers": {
    "x-source": "SALESFORCE",
  "x-transactionId": vars.vTransactionId,
  "x-msg-timestamp": (now() as LocalDateTime {format: "yyyy-MM-dd'T'HH:mm:ss.000'Z'"}),
  "correlationId": vars.vCorrelationId,
  "sourceId": "SALESFORCE_EXP_API",
  "destinationId": "TRANSACTION_DB_SYS_API",
  "content-type": "application/json"
 }
}
```

#### HTTP Request: Insert into REF_ID

- Method: POST
- Path: `/api/REF_ID`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Body: ```json
   {
      "refID":{
      "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type'),
      "SALESFORCE_ID": vars.vSalesforcePayload.'AccountID__c',
      "LAST_UPDATED_BY": "EXPERIENCE_API"
      }
   }```
- Target Variable: `vInsertRefIdResponse`

#### Transform Message: Set vTransactionDetails Record

```json
{
    "transaction": {
  "CORRELATION_ID": vars.vCorrelationId,
     "OPERATION": "CREATE",
     "SOURCE": "SALESFORCE",
     "STATUS": "QUEUED",
     "LAST_UPDATED_BY": "EXPERIENCE_API",
     "ENTERPRISE_ID":  vars.vInsertRefIdResponse.response.'ENTERPRISE_ID', 
     "PAYLOAD": write(vars.vSalesforcePayload,'application/json'),
     "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type') default null,
     "RETRY_COUNT": 0,
     "QUERY_PARAMS": null,
     "ERROR_MSG": null,
     "ERROR_TYPE": null
   }
}
```

#### HTTP Request: Insert into TRANSACTION_DETAILS

- Method: POST
- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Path: `p('https.request.dbSysApi.transactionDetails.path')`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Body: `vars.vTransactionDetailsRecord`
- Target Variable: `vInsertTransactionResponse`

#### Payload

```json
{
  "transactionsTableResponse": vars.vInsertTransactionResponse
}
```

#### Logger: LOG INFO: Discard sync

```json
{
 "Message": (
  if(vars.vSalesforcePayload.'LastModifiedById' == Mule::p('salesforce.user.mulesoft.id'))
   "Account has been created by Mule flow. NO Sync is required for this contact"
  else
   "Account has been set to DO_NOT_SYNC. NO Sync is required for this contact"
 ),
 "FlowName": "pf-on-sf-account-created",
 "CorrelationID": vars.vCorrelationId,
 "Endpoint": "Invoked via Salesforce trigger"
}
```

### End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit
