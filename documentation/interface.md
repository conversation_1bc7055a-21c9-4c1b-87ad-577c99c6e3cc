# Interface

## APIKitRouter: 3degreesSalesforceExpAPI-main

### Transform Message: Set Attributes

> gets attributes defined in api endpoints

```json
{
 "headers": attributes.headers,
 "queryParams": attributes.queryParams,
 "uriParams": attributes.uriParams
}
```

## Endpoints

> all endpoints call flows defined in IMPLEMENTATION flows

### get:\notifyDelete

- FlowReference: [pf-notify-account-delete](pf-notify-account-delete.md)