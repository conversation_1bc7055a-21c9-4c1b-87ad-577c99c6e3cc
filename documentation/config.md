# Configuration

## config

> `.properties` files contain properties used in [global](documentation/global.md) Global ELements Configuration  
> `*-secure.properties` files contain *encrypted* properties used in [global](documentation/global.md) Global Elements Configuration  
> TLS Context configurations use JKS Key Store [Certificates](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/certificates)

[config-common.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-common.properties)  

DEV  
- [config-dev.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-dev.properties)  
- [config-dev-secure.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-dev-secure.properties)  

LOCAL  
- [config-local.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-local.properties)    
- [config-local-secure.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-local-secure.properties) 

PROD  
- [config-prod.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-prod.properties)    
- [config-prod-secure.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-prod-secure.properties) 

TEST  
- [config-test.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-test.properties)    
- [config-test-secure.properties](https://github.com/3DegreesGroupInc/Apisero_Salesforce_EXP_API/blob/feature/src/main/resources/config/config-test-secure.properties) 
