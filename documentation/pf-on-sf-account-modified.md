# pf-on-sf-account-modified

## Flow: pf-on-sf-account-modified

### On Modified Object: On Account Record Modified

- Configuration: Salesforce_Config
- Object Type: Account
- Scheduling Strategy: Fixed Frequency
  - Frequency: `${salesforce.poll.frequency}`
  - Start Delay: `${salesforce.poll.startDelay}`
  - Time Unit: `MILLISECONDS`

### Logger: LOG INFO: Timestamps

- Level: INFO
- Message: ```json
{
 "Id": payload.'Id',
 "Name": payload.'Name',
 "LastModifiedDate": payload.'LastModifiedDate',
 "CreatedDate": payload.'CreatedDate'
}```

### Choice: Check if record is created

```mermaid
flowchart LR
    A{Check if record is created}
    subgraph When[When abs lastModifiedDate - createdDate.seconds > 'salesforce.createTimeDiff']
    W1[[Flow Reference: sf-create-record-in-db]]
    end 
    subgraph Default
    end
    A --> When
    A --> Default
```

#### End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit

## Flow: sf-create-record-in-db

### Transform Message: Set vAttribute

```json
{
 "headers": attributes.headers,
 "queryParams": attributes.queryParams,
 "uriParams": attributes.uriParams
}
```

### Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

### Choice: Check LastModifiedById

```mermaid
flowchart LR
    A{Check if record is created}
    subgraph When[When payload.'LastModifiedById' != 'salesforce.user.mulesoft.id']
    W1[Variable: vRequestAttributes]
    W2[HTTP Request: Fetch ENTERPRISE_ID from REF_ID]
    W3{Check DO_NOT_SYNC}
    subgraph W31[When vars.vSalesforcePayload.'DoNotSync__c' == false AND vars.vRefIdResponse.'response' not exists OR NOT vars.vRefIdResponse.response.'DO_NOT_SYNC]
    W311[Transform Message: Set vTransactionDetailsRecord]
    W312[HTTP Request: Insert into TRANSACTION_DETAIL]
    W313[Payload: transactionsTableResponse]
    W311 --> W312 --> W313
    end
    subgraph W3Default[Default]
    W3DL[LOG INFO: Discard sync]
    end
    W1 --> W2 --> W3 
    W3 --> W31
    W3 --> W3Default 
    end 
    subgraph Default
    end
    A --> When
    A --> Default
```

#### Variable: vRequestAttributes

```json
{
 "headers": {
  "x-source": "SALESFORCE",
  "x-transactionId": vars.vTransactionId,
  "x-msg-timestamp": (now() as LocalDateTime {format: "yyyy-MM-dd'T'HH:mm:ss.000'Z'"}),
  "correlationId": vars.vCorrelationId,
  "sourceId": "SALESFORCE_EXP_API",
  "destinationId": "TRANSACTION_DB_SYS_API",
  "content-type": "application/json"
 }
}
```

#### HTTP Request: Fetch ENTERPRISE_ID from REF_ID

- Method: GET
- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Path: `p('https.request.dbSysApi.refId.path')`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Query Parameters: ```json
{
  "ARTEMIS_ID": vars.vSalesforceId,
  "OBJECT_TYPE": vars.vObjectType
}```
- Target Variable: `vRefIdResponse`

#### Transform Message: vTransactionalDetailsRecord

```json
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "UPDATE",
    "SOURCE": "SALESFORCE",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID":  vars.vRefIdResponse.response[0].'ENTERPRISE_ID', 
    "PAYLOAD": write(vars.vSalesforcePayload,'application/json'),
    "OBJECT_TYPE": upper(vars.vSalesforcePayload.'type') default null,
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
     "key": ($$),
  "val": ($)
 }) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null
  }
}
```

#### HTTP Request: Insert into TRANSACTION_DETAILS

- Method: POST
- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Path: `p('https.request.dbSysApi.transactionDetails.path')`
- Headers: `vars.vRequestAttributes.'headers' default ""`
- Body: `vars.vTransactionDetailsRecord`
- Target Variable: `vInsertTransactionResponse`

#### Payload transactionsTableResponse

```json
{
 "transactionsTableResponse": vars.vInsertResponse
}
```

#### Logger: LOG INFO: Discard sync

- Level: INFO
- Message: ```json{
 "Message": "Account has been set to DO_NOT_SYNC. NO Sync is required for this contact",
 "FlowName": "pf-on-sf-account-modified",
 "CorrelationID": vars.vCorrelationId,
 "Endpoint": "Invoked via Salesforce trigger"
}

```
