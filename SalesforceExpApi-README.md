# Salesforce Experience API

A C# .NET 8 implementation migrated from MuleSoft, providing REST API endpoints for Salesforce data synchronization and processing.

## Overview

This project is a complete migration from MuleSoft to C#/.NET, maintaining all the original functionality while leveraging modern .NET features and best practices.

### Key Features

- **REST API Endpoints**: Complete HTTP API for order, invoice, and account operations
- **Event Processing**: Background services for handling Salesforce events
- **Error Handling**: Global exception handling with structured error responses
- **Logging**: MuleSoft-style JSON logging with correlation tracking
- **Authentication**: Client ID enforcement and JWT bearer authentication
- **Health Checks**: Comprehensive health monitoring
- **Rate Limiting**: Built-in API rate limiting
- **Docker Support**: Containerized deployment ready

## Architecture

The solution follows Clean Architecture principles:

```
├── SalesforceExpApi.Api/          # Web API layer
├── SalesforceExpApi.Core/         # Business logic and domain models
├── SalesforceExpApi.Infrastructure/ # External integrations
├── SalesforceExpApi.Logging/      # Logging infrastructure
└── SalesforceExpApi.EventProcessing/ # Background event processing
```

## Getting Started

### Prerequisites

- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Docker (optional)

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SalesforceExpApi
   ```

2. **Restore dependencies**
   ```bash
   dotnet restore
   ```

3. **Update configuration**
   - Copy `src/SalesforceExpApi.Api/appsettings.json` to `appsettings.Development.json`
   - Update connection strings and API endpoints
   - Configure Salesforce credentials

4. **Run the application**
   ```bash
   dotnet run --project src/SalesforceExpApi.Api
   ```

5. **Access Swagger UI**
   - Navigate to `https://localhost:5001` or `http://localhost:5000`

### Using Docker

1. **Build the Docker image**
   ```bash
   docker build -f docker/Dockerfile -t salesforce-exp-api .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose -f docker/docker-compose.yml up
   ```

## API Endpoints

### Orders
- `POST /api/orders` - Create a new order
- `PUT /api/orders` - Update an existing order
- `DELETE /api/orders?orderNetsuiteId={id}` - Delete an order

### Invoices
- `PUT /api/invoices` - Update an invoice

### Account Management
- `GET /api/notifyDelete` - Process account deletion notification

### Health Checks
- `GET /health` - Basic health check
- `GET /api/health/detailed` - Detailed health status

## Configuration

### Key Configuration Sections

```json
{
  "HttpClients": {
    "TransactionDbApi": { ... },
    "SyncPrcApi": { ... },
    "OrderPrcApi": { ... }
  },
  "Salesforce": {
    "ConsumerKey": "...",
    "ConsumerSecret": "...",
    "Username": "...",
    "Password": "...",
    "SecurityToken": "..."
  },
  "Authentication": {
    "ClientIdEnforcement": { ... },
    "Jwt": { ... }
  }
}
```

### Environment Variables

- `ASPNETCORE_ENVIRONMENT` - Environment (Development, Staging, Production)
- `ASPNETCORE_URLS` - Binding URLs
- `ConnectionStrings__DefaultConnection` - Database connection string

## Testing

### Run Unit Tests
```bash
dotnet test
```

### Run Specific Test Project
```bash
dotnet test tests/SalesforceExpApi.Core.Tests
```

### Generate Coverage Report
```bash
dotnet test --collect:"XPlat Code Coverage"
```

## Deployment

### Using PowerShell Script
```powershell
.\scripts\build.ps1 -Configuration Release
```

### Manual Deployment
```bash
dotnet publish src/SalesforceExpApi.Api -c Release -o ./publish
```

### Docker Deployment
```bash
docker-compose -f docker/docker-compose.yml up -d
```

## Monitoring and Observability

### Logging
- Structured JSON logging
- Correlation ID tracking
- MuleSoft-compatible log format
- Configurable log levels

### Health Checks
- Application health status
- External dependency checks
- Custom health check endpoints

### Metrics
- Built-in ASP.NET Core metrics
- Custom business metrics
- Application Insights integration (optional)

## Security

### Authentication
- Client ID enforcement
- JWT Bearer token validation
- Custom authentication middleware

### HTTPS
- TLS/SSL support
- Certificate-based authentication
- Secure headers

### Rate Limiting
- Configurable rate limits
- Per-client rate limiting
- Queue-based overflow handling

## Migration from MuleSoft

### Key Differences

| MuleSoft | C#/.NET |
|----------|---------|
| DataWeave transformations | LINQ + helper methods |
| Flow-based processing | Service-based architecture |
| Mule runtime | ASP.NET Core runtime |
| Anypoint Studio | Visual Studio/VS Code |
| CloudHub deployment | Docker/Kubernetes |

### Migration Benefits
- **Type Safety**: Compile-time error checking
- **Performance**: Better runtime performance
- **Tooling**: Rich debugging and profiling tools
- **Ecosystem**: Extensive .NET ecosystem
- **Cost**: Reduced licensing costs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions and support:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `docs/` folder
