# C# Project Structure for MuleSoft Migration

## Recommended Solution Structure

```
SalesforceExpApi.sln
├── src/
│   ├── SalesforceExpApi.Api/                    # Main Web API project
│   │   ├── Controllers/
│   │   │   ├── SalesforceExpApiController.cs    # Main API controller
│   │   │   └── HealthController.cs              # Health check endpoint
│   │   ├── Middleware/
│   │   │   ├── CorrelationIdMiddleware.cs       # Request correlation tracking
│   │   │   ├── GlobalErrorHandlerFilter.cs     # Global error handling
│   │   │   └── AuthenticationMiddleware.cs      # Client ID enforcement
│   │   ├── Attributes/
│   │   │   ├── CorrelationIdFilter.cs          # Action filter for correlation
│   │   │   └── ValidationAttributes.cs         # Custom validation attributes
│   │   ├── Configuration/
│   │   │   ├── HttpClientConfiguration.cs      # HTTP client setup
│   │   │   ├── AuthenticationConfiguration.cs  # Auth configuration
│   │   │   └── SwaggerConfiguration.cs         # API documentation
│   │   ├── Program.cs                          # Application entry point
│   │   ├── appsettings.json                    # Configuration files
│   │   ├── appsettings.Development.json
│   │   ├── appsettings.Test.json
│   │   └── appsettings.Production.json
│   │
│   ├── SalesforceExpApi.Core/                   # Business logic and domain models
│   │   ├── Models/
│   │   │   ├── Requests/
│   │   │   │   ├── CreateOrderRequest.cs
│   │   │   │   ├── UpdateOrderRequest.cs
│   │   │   │   ├── UpdateInvoiceRequest.cs
│   │   │   │   └── NotifyDeleteRequest.cs
│   │   │   ├── Responses/
│   │   │   │   ├── CreateOrderResponse.cs
│   │   │   │   ├── UpdateOrderResponse.cs
│   │   │   │   ├── DeleteOrderResponse.cs
│   │   │   │   ├── UpdateInvoiceResponse.cs
│   │   │   │   └── NotifyDeleteResponse.cs
│   │   │   ├── Domain/
│   │   │   │   ├── Order.cs
│   │   │   │   ├── Invoice.cs
│   │   │   │   ├── Account.cs
│   │   │   │   ├── Product.cs
│   │   │   │   └── Opportunity.cs
│   │   │   └── Common/
│   │   │       ├── ErrorResponse.cs
│   │   │       ├── BaseResponse.cs
│   │   │       └── TransactionDetails.cs
│   │   ├── Services/
│   │   │   ├── Interfaces/
│   │   │   │   ├── IOrderProcessingService.cs
│   │   │   │   ├── IInvoiceProcessingService.cs
│   │   │   │   ├── IAccountDeletionService.cs
│   │   │   │   ├── ISalesforceEventService.cs
│   │   │   │   └── IDataTransformationService.cs
│   │   │   ├── OrderProcessingService.cs
│   │   │   ├── InvoiceProcessingService.cs
│   │   │   ├── AccountDeletionService.cs
│   │   │   ├── SalesforceEventService.cs
│   │   │   └── DataTransformationService.cs
│   │   ├── Exceptions/
│   │   │   ├── DataValidationException.cs
│   │   │   ├── ExternalServiceException.cs
│   │   │   ├── DataWeaveTransformationException.cs
│   │   │   └── RecordDependencyException.cs
│   │   └── Extensions/
│   │       ├── ServiceCollectionExtensions.cs
│   │       └── ObjectExtensions.cs
│   │
│   ├── SalesforceExpApi.Infrastructure/          # External integrations and data access
│   │   ├── HttpClients/
│   │   │   ├── Interfaces/
│   │   │   │   ├── ITransactionDbApiClient.cs
│   │   │   │   ├── ISyncPrcApiClient.cs
│   │   │   │   └── IOrderPrcApiClient.cs
│   │   │   ├── TransactionDbApiClient.cs
│   │   │   ├── SyncPrcApiClient.cs
│   │   │   └── OrderPrcApiClient.cs
│   │   ├── Salesforce/
│   │   │   ├── ISalesforceClient.cs
│   │   │   ├── SalesforceClient.cs
│   │   │   ├── SalesforceEventListener.cs
│   │   │   └── SalesforceConfiguration.cs
│   │   ├── Configuration/
│   │   │   ├── HttpClientSettings.cs
│   │   │   ├── SalesforceSettings.cs
│   │   │   └── CertificateSettings.cs
│   │   └── Extensions/
│   │       ├── HttpClientExtensions.cs
│   │       └── RetryPolicyExtensions.cs
│   │
│   ├── SalesforceExpApi.Logging/                # Logging infrastructure
│   │   ├── IMuleSoftLogger.cs
│   │   ├── MuleSoftLogger.cs
│   │   ├── ICorrelationService.cs
│   │   ├── CorrelationService.cs
│   │   └── Extensions/
│   │       └── LoggingExtensions.cs
│   │
│   └── SalesforceExpApi.EventProcessing/        # Background services for Salesforce events
│       ├── Services/
│       │   ├── AccountEventProcessor.cs
│       │   ├── ProductEventProcessor.cs
│       │   ├── OpportunityEventProcessor.cs
│       │   └── BaseEventProcessor.cs
│       ├── Models/
│       │   ├── SalesforceEvent.cs
│       │   ├── AccountEvent.cs
│       │   ├── ProductEvent.cs
│       │   └── OpportunityEvent.cs
│       ├── Handlers/
│       │   ├── IEventHandler.cs
│       │   ├── AccountCreatedHandler.cs
│       │   ├── AccountModifiedHandler.cs
│       │   ├── ProductCreatedHandler.cs
│       │   ├── ProductModifiedHandler.cs
│       │   └── OpportunityModifiedHandler.cs
│       └── Configuration/
│           └── EventProcessingConfiguration.cs
│
├── tests/
│   ├── SalesforceExpApi.Api.Tests/              # API integration tests
│   │   ├── Controllers/
│   │   │   └── SalesforceExpApiControllerTests.cs
│   │   ├── Middleware/
│   │   │   └── GlobalErrorHandlerTests.cs
│   │   └── Integration/
│   │       └── ApiIntegrationTests.cs
│   │
│   ├── SalesforceExpApi.Core.Tests/             # Unit tests for business logic
│   │   ├── Services/
│   │   │   ├── OrderProcessingServiceTests.cs
│   │   │   ├── InvoiceProcessingServiceTests.cs
│   │   │   └── AccountDeletionServiceTests.cs
│   │   └── Models/
│   │       └── ModelValidationTests.cs
│   │
│   ├── SalesforceExpApi.Infrastructure.Tests/   # Infrastructure tests
│   │   ├── HttpClients/
│   │   │   ├── TransactionDbApiClientTests.cs
│   │   │   ├── SyncPrcApiClientTests.cs
│   │   │   └── OrderPrcApiClientTests.cs
│   │   └── Salesforce/
│   │       └── SalesforceClientTests.cs
│   │
│   └── SalesforceExpApi.EventProcessing.Tests/  # Event processing tests
│       ├── Handlers/
│       │   ├── AccountEventHandlerTests.cs
│       │   ├── ProductEventHandlerTests.cs
│       │   └── OpportunityEventHandlerTests.cs
│       └── Services/
│           └── EventProcessorTests.cs
│
├── docs/
│   ├── api-documentation.md
│   ├── deployment-guide.md
│   ├── migration-notes.md
│   └── configuration-guide.md
│
├── scripts/
│   ├── build.ps1
│   ├── deploy.ps1
│   └── setup-certificates.ps1
│
├── docker/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.override.yml
│
└── .github/
    └── workflows/
        ├── ci.yml
        ├── cd.yml
        └── security-scan.yml
```

## Key Design Principles

### 1. **Clean Architecture**
- **API Layer**: Controllers, middleware, filters
- **Core Layer**: Business logic, domain models, interfaces
- **Infrastructure Layer**: External integrations, data access
- **Cross-cutting Concerns**: Logging, event processing

### 2. **Dependency Injection**
- All services registered in DI container
- Interface-based design for testability
- Configuration through options pattern

### 3. **Configuration Management**
- Environment-specific appsettings files
- Secure configuration for sensitive data
- Configuration validation on startup

### 4. **Error Handling**
- Global exception handling middleware
- Structured error responses
- Comprehensive logging

### 5. **Observability**
- Correlation ID tracking
- Structured logging (JSON format)
- Health checks and metrics
- Distributed tracing support

## Project Dependencies

### Main API Project (SalesforceExpApi.Api)
```xml
<PackageReference Include="Microsoft.AspNetCore.App" />
<PackageReference Include="Swashbuckle.AspNetCore" />
<PackageReference Include="Serilog.AspNetCore" />
<PackageReference Include="Serilog.Sinks.Console" />
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
```

### Core Business Logic (SalesforceExpApi.Core)
```xml
<PackageReference Include="FluentValidation" />
<PackageReference Include="AutoMapper" />
<PackageReference Include="System.ComponentModel.Annotations" />
```

### Infrastructure (SalesforceExpApi.Infrastructure)
```xml
<PackageReference Include="Polly" />
<PackageReference Include="Polly.Extensions.Http" />
<PackageReference Include="Microsoft.Extensions.Http" />
<PackageReference Include="Salesforce.NET" />
```

### Event Processing (SalesforceExpApi.EventProcessing)
```xml
<PackageReference Include="Microsoft.Extensions.Hosting" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" />
```

## Deployment Considerations

### 1. **Containerization**
- Docker support for consistent deployments
- Multi-stage builds for optimized images
- Health check endpoints

### 2. **Configuration**
- Environment variables for deployment-specific settings
- Azure Key Vault integration for secrets
- Configuration validation

### 3. **Monitoring**
- Application Insights integration
- Custom metrics and telemetry
- Log aggregation and analysis

### 4. **Security**
- HTTPS enforcement
- Client certificate authentication
- API key validation
- CORS configuration
